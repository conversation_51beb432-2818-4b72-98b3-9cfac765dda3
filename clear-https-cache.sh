#!/bin/bash

echo "🔧 Limpando caches para resolver problema de HTTPS forçado..."

cd app

echo "📝 Limpando cache de configuração..."
php artisan config:clear

echo "📝 Limpando cache de rotas..."
php artisan route:clear

echo "📝 Limpando cache de views..."
php artisan view:clear

echo "📝 Limpando cache geral..."
php artisan cache:clear

echo "📝 Limpando cache de autoload do Composer..."
composer dump-autoload

echo "✅ Todos os caches foram limpos!"
echo ""
echo "🔍 Verificando configurações atuais:"
echo "FORCE_HTTPS: $(grep FORCE_HTTPS .env)"
echo "SESSION_SECURE_COOKIE: $(grep SESSION_SECURE_COOKIE .env)"
echo "APP_URL: $(grep APP_URL .env)"
echo ""
echo "🚀 Agora teste sua aplicação!"
