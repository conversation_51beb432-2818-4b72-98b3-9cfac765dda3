#!/bin/bash

echo "Alterando configuração para HTTP..."

# Backup da configuração HTTPS atual
cp docker/nginx/app.conf docker/nginx/app-https-backup.conf

# Alterar porta no docker-compose.yml
sed -i 's/"8443:443"/"8000:80"/g' docker-compose.yml

# Alterar configuração do Nginx
sed -i 's/listen 443 ssl http2;/listen 80;/g' docker/nginx/app.conf
sed -i '/ssl_certificate/d' docker/nginx/app.conf
sed -i '/ssl_protocols/d' docker/nginx/app.conf
sed -i '/ssl_ciphers/d' docker/nginx/app.conf
sed -i '/ssl_prefer_server_ciphers/d' docker/nginx/app.conf
sed -i '/ssl_session_cache/d' docker/nginx/app.conf
sed -i '/ssl_session_timeout/d' docker/nginx/app.conf
sed -i '/Strict-Transport-Security/d' docker/nginx/app.conf
sed -i 's/fastcgi_param HTTPS on;/fastcgi_param HTTPS off;/g' docker/nginx/app.conf
sed -i '/fastcgi_param SERVER_PORT 443;/d' docker/nginx/app.conf

echo "Reiniciando containers..."
sudo docker compose down
sudo docker compose up -d

echo "✅ Alterado para HTTP!"
echo "🌐 Acesse: http://localhost:8000"
