# Configuração HTTPS - WinCash

## ✅ HTTPS Configurado com Sucesso!

Sua aplicação WinCash agora está configurada para rodar em HTTPS.

### 🚀 Como usar:

1. **Para iniciar com HTTPS:**
   ```bash
   docker compose down
   docker compose up -d
   ```
   Acesse: https://localhost:8443

2. **Para voltar ao HTTP (quando necessário):**
   ```bash
   ./switch-to-http.sh
   ```
   Acesse: http://localhost:8000

3. **Para voltar ao HTTPS:**
   ```bash
   ./switch-to-https.sh
   ```
   Acesse: https://localhost:8443

### 📋 O que foi configurado:

- ✅ Porta HTTPS: 8443
- ✅ Certificados SSL auto-assinados gerados
- ✅ Nginx configurado para HTTPS
- ✅ Headers de segurança adicionados
- ✅ HSTS (HTTP Strict Transport Security) habilitado
- ✅ Parâmetros PHP ajustados para HTTPS

### ⚠️ Importante:

- O certificado é auto-assinado, então seu navegador mostrará um aviso de segurança
- Clique em "Avançado" e "Prosseguir para localhost" para continuar
- Para produção, substitua pelos certificados válidos do Let's Encrypt

### 📁 Arquivos modificados:

- `docker-compose.yml` - Porta alterada para 8443
- `docker/nginx/app.conf` - Configuração HTTPS
- `docker/nginx/ssl/` - Certificados SSL
- Scripts de alternância entre HTTP/HTTPS

### 🔧 Comandos úteis:

```bash
# Ver status dos containers
docker compose ps

# Ver logs do Nginx
docker compose logs nginx

# Regenerar certificados SSL
./generate-ssl.sh
```
