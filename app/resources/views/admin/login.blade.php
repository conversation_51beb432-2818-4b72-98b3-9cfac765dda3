@extends('layouts.auth')

@section('content')



<!-- Modern Two-Column Login Layout -->
<div class="container">
    <!-- Left Column - Login Form -->
    <div class="login-section">
        <!-- Logo/Brand -->
        <img src="{{url('img/logo.png')}}" alt="WinCash" class="logo">

        @if(!$fazersenha)
            <!-- Login Form -->
            <div id="login-content" class="login-form-content">
                <h2>Acesso ao Painel</h2>


                @if(session('error'))
                    <div class="alert alert-error">
                        {{ session('error') }}
                    </div>
                @endif

                @if(session('success'))
                    <div class="alert alert-success">
                        {{ session('success') }}
                    </div>
                @endif

                <form action="{{route('login')}}" method="POST" id="form-login" class="login-form" aria-label="Formulário de login">
                    {{ csrf_field() }}

                    <div class="input-field">
                        <label for="email">E-mail</label>
                        <input type="email" name="email" id="email" placeholder="E-mail" required="required" />
                    </div>

                    <div class="input-field">
                        <label for="senha">Senha</label>
                        <div class="password-container">
                            <input type="password" name="senha" id="senha" placeholder="Senha" required="required" />
                            <span class="toggle-password" id="toggle-password" title="Mostrar/Ocultar senha">
                                <i class="fa fa-eye" id="eye-icon"></i>
                            </span>
                        </div>
                    </div>

                    <div class="remember-me">
                        <input type="checkbox" id="manter_logado" name="manter_logado" value="S">
                        <label for="manter_logado">Manter Logado</label>
                    </div>

                    <button type="submit" class="btn-login">
                        ENTRAR
                    </button>
                </form>

                <div class="forgot-password">
                    <button type="button" onclick="$('#login-content').hide(); $('#forgot-content').fadeIn();">
                        Esqueceu a senha?
                    </button>
                </div>
            </div>

            <!-- Forgot Password Form -->
            <div id="forgot-content" class="forgot-password-content" style="display:none">
                <h2>Recuperar Senha</h2>

                <form action="{{route('lembrarsenha')}}" method="POST" id="form-lembrarsenha" class="login-form" aria-label="Formulário de recuperação de senha">
                    {{ csrf_field() }}

                    <div class="input-field">
                        <label for="email-recovery">E-mail</label>
                        <input type="email" name="email" id="email-recovery" placeholder="Digite seu e-mail" required="required" />
                    </div>

                    <button type="submit" class="btn-login">
                        RECUPERAR SENHA
                    </button>
                </form>

                <div class="back-to-login">
                    <button type="button" onclick="$('#forgot-content').hide(); $('#login-content').fadeIn();">
                        Voltar para o login
                    </button>
                </div>
            </div>
        @else
            <!-- Reset Password Form -->
            <div id="reset-content" class="reset-password-content">
                <h2>Nova Senha</h2>

                <div class="user-email">
                    {{$fazersenha->email}}
                </div>

                <form action="{{route('lembrarsenha')}}" method="POST" id="form-novasenha" class="login-form" aria-label="Formulário de nova senha">
                    {{ csrf_field() }}
                    <input type="hidden" name="token" value="{{$fazersenha->token}}" />
                    <input type="hidden" name="email" value="{{$fazersenha->email}}" />

                    <div class="input-field">
                        <label for="nova-senha">Nova Senha</label>
                        <div class="password-container">
                            <input type="password" name="senha" id="nova-senha" placeholder="Digite sua nova senha" required="required" />
                            <span class="toggle-password" id="toggle-password-reset" title="Mostrar/Ocultar senha">
                                <i class="fa fa-eye" id="eye-icon-reset"></i>
                            </span>
                        </div>
                    </div>

                    <button type="submit" class="btn-login">
                        SALVAR SENHA
                    </button>
                </form>
            </div>
        @endif
    </div>

    <!-- Right Column - Hero Section -->
    <div class="info-section">
        <div class="illustration">
            <img src="{{url('img/ilustration.svg')}}" alt="Ilustração">
        </div>
    </div>
</div>
@stop
