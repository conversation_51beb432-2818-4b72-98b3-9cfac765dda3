@extends('layouts.admin')



@section('content')
                <div class="bg-white rounded shadow p-3 mb-5">


                <div>
                    <h1><i class="fa fa-store-alt"></i> Clientes</h1>
                    <a href="{{route('categorias')}}" class="btn btn-sm btn-light"><i class="fa fa-folder-open"></i> Categorias</a>
                    <br /><br />


            		<form action="{{route('clientes-salvar')}}" method="POST" id="form-clientes" role="form" class="form form-inline formloading">
                    {{ csrf_field() }}

                      <div class="form-row">
                          <div class="form-group col-auto">
                            @if(!empty($registro->id))
                                <div class="btn btn-sm btn-secondary"><i class="fa fa-edit"></i> Alterar registro</div>
                                <input type="hidden" name="id" value="{{$registro->id}}" />
                            @else
                                <div class="btn btn-sm btn-light"><i class="fa fa-save"></i> Adicionar registro</div>
                            @endif
                          </div>

                          <div class="form-group col-auto">
                            <select name="categorias_id" class="form-control form-control-sm" required="required">
                              <option value="">-- Categoria --</option>
                              @foreach($categorias as $categoria)
                                <option value="{{$categoria->id}}" @if($registro->categorias_id == $categoria->id)selected="selected" @endif >
                                    {{$categoria->nome}}
                                </option>
                              @endforeach
                            </select>
                          </div>

                          <div class="form-group col-auto">
                            <input type="text" name="nome" class="form-control form-control-sm" id="nome" placeholder="Nome" value="{{$registro->nome}}" required="required" />
                          </div>
                          <div class="form-group col-auto">
                            <input type="email" name="email" class="form-control form-control-sm" id="email" placeholder="Email" value="{{$registro->email}}" required="required" />
                          </div>
                          <div class="form-group col-auto">
                            <input type="text" name="celular" class="form-control form-control-sm telefone" id="celular" placeholder="Celular" value="{{$registro->celular}}" required="required" />
                          </div>
                          <div class="form-group col-auto">
                            <input type="text" name="cnpj" class="form-control form-control-sm" id="cnpj" placeholder="CNPJ" value="{{$registro->cnpj}}" />
                          </div>
                          <div class="form-group col-auto">
                            <input type="text" name="url" class="form-control form-control-sm" id="url" placeholder="URL" value="{{$registro->url}}" />
                          </div>
                    </div>
                    <div class="w-100 m-2"></div>
                    <div class="form-row">

                        <div class="form-group col-auto">
                            <input type="text" name="endereco" class="form-control form-control-sm" id="endereco" placeholder="Endereço" value="{{$registro->endereco}}" />
                        </div>
                        <div class="form-group col-auto">
                            <input type="text" name="cidade" class="form-control form-control-sm" id="cidade" placeholder="Cidade" value="{{$registro->cidade}}" />
                        </div>
                        <div class="form-group col-auto">
                            <input type="text" name="estado" class="form-control form-control-sm" id="estado" placeholder="UF" value="{{$registro->estado}}" maxlength="2" size="2" />
                        </div>
                        <div class="form-group col-auto">
                            <input type="text" name="cep" class="form-control form-control-sm cep" id="cep" placeholder="CEP" value="{{$registro->cep}}" />
                        </div>

                        <div class="form-group col-auto">
                            <label>Pontos para o primeiro cadastro</label>
                            <input name="pontos_cadastro" class="form-control form-control-sm ml-2" id="pontos_cadastro" placeholder="0" value="{{$registro->pontos_cadastro}}" />
                        </div>

                    </div>

                    @if(!empty($registro->id))
                    <div class="w-100 m-2"></div>
                    <div class="form-row">
                        <h5>Configurações: </h5>
                        <div class="form-group col-auto">
                            <input type="number" step="0.01" name="conversao" class="form-control form-control-sm" pattern="[0-9,.]*" id="conversao" placeholder="Conversão R$ para Pontos" value="{{$registro->conversao}}" />
                        </div>

                        <div class="form-group col-auto">
                            <input type="number" step="1" name="diasvalidade" class="form-control form-control-sm" pattern="[0-9]*" id="diasvalidade" placeholder="Dia validade pontos" value="{{$registro->diasvalidade}}" />
                        </div>

                        <div class="form-group col-auto">
                            <input type="number" step="0.01" name="reaiscashback" class="form-control form-control-sm" pattern="[0-9,.]*" id="reaiscashback" placeholder="Pontos para resgate" value="{{$registro->reaiscashback}}" />
                        </div>

                        <div class="form-group col-auto">
                            <input type="text" name="onesignal_api_key" class="form-control form-control-sm" id="onesignal_api_key" placeholder="OneSignal API Key" value="{{$registro->onesignal_api_key ?? ''}}" />
                        </div>

                        <div class="form-group col-auto">
                            <input type="text" name="tags" class="form-control form-control-sm" id="tags" placeholder="Tags (ex: VIP, Aplicativo, Comum)" value="{{$registro->tags ?? ''}}" />
                            <small class="form-text text-muted">Separe as tags por vírgula</small>
                        </div>



                        @if($registro->premios())
                        <div class="form-group col-auto">
                          <select name="premios_id" class="form-control form-control-sm">
                            <option value="">-- Prêmio do Cashback --</option>
                            @foreach($registro->premios as $premio)
                              <option value="{{$premio->id}}" @if($registro->premios_id == $premio->id)selected="selected" @endif >
                                [{{number_format($premio->pontos,0,'','')}}] {{$premio->titulo}}
                              </option>
                            @endforeach
                          </select>
                        </div>
                        @endif

                        <div class="d-flex col-6 mt-2">
                            <h5>Cores: </h5>
                            &nbsp;&nbsp;&nbsp;
                            1: <input class="mr-2 ml-2" type="color" name="bg_color" value="{{ !empty($registro->bg_color) ? $registro->bg_color : '' }}"/>
                            2: <input class="mr-2 ml-2" type="color" name="bg_color2" value="{{ !empty($registro->bg_color2) ? $registro->bg_color2 : '' }}"/>
                            <label for="cor_padrao">Usar cor padrão</label>
                            <input class="ml-2" type="checkbox" id="cor_padrao" name="cor_padrao">
                        </div>

                    </div>
                    @endif

                    <div class="w-100 m-2"></div>
                    <div class="form-row">
                        <div class="form-group col-auto input-loading">
                            <input type="submit" class="btn btn-sm btn-success" value="Salvar" />
                            @if(!empty($registro->id))
                                &nbsp;<a href="{{route('clientes',['','page'=>$page])}}" class="btn btn-sm btn-secondary editar">Cancelar</a>
                            @endif
                        </div>
                    </div>

                    </form>


                    <hr />

                    <!--
                    ***
                    *** FILTRO
                    ***
                    -->
            		<form action="{{route('clientes-filtrar')}}" method="POST" id="form-filtro-clientes" role="form" class="form form-inline formloading">
                        {{ csrf_field() }}

                          <div class="form-row">
                              <div class="form-group col-auto">
                                <div class="btn btn-sm btn-light"><i class="fa fa-filter"></i> Filtrar</div>
                              </div>


                              <div class="form-group col-auto">
                                <input type="text" name="termo" class="form-control form-control-sm" placeholder="Termo" id="termo" value="{{$termo}}" />
                              </div>

                              <div class="form-group col-auto">
                                <select name="tag_filtro" class="form-control form-control-sm">
                                  <option value="">-- Filtrar por Tag --</option>
                                  <option value="vip" @if(request('tag_filtro') == 'vip') selected @endif>VIP</option>
                                  <option value="aplicativo" @if(request('tag_filtro') == 'aplicativo') selected @endif>Aplicativo</option>
                                  <option value="comum" @if(request('tag_filtro') == 'comum') selected @endif>Comum</option>
                                  <option value="premium" @if(request('tag_filtro') == 'premium') selected @endif>Premium</option>
                                  <option value="novo" @if(request('tag_filtro') == 'novo') selected @endif>Novo</option>
                                  <option value="inativo" @if(request('tag_filtro') == 'inativo') selected @endif>Inativo</option>
                                </select>
                              </div>

                              <div class="form-group col-auto input-loading">
                                <input type="submit" class="btn btn-sm btn-secondary" value="Filtrar" />
                              </div>

                              <div class="form-group col-auto">
                                <a href="{{route('clientes-aplicar-tags-automaticas')}}" class="btn btn-sm btn-info" title="Aplicar tags automáticas baseadas em critérios">
                                  <i class="fa fa-tags"></i> Tags Auto
                                </a>
                              </div>

                              <div class="form-group col-auto">
                                <strong>Tags:</strong>
                                <span class="badge badge-warning">VIP</span>
                                <span class="badge badge-info">Aplicativo</span>
                                <span class="badge badge-secondary">Comum</span>
                                <span class="badge badge-success">Premium</span>
                                <span class="badge badge-primary">Novo</span>
                                <span class="badge badge-danger">Inativo</span>
                              </div>

                              <div class="form-group col-auto">

                                <span class="badge badge-success">menor 30 dias</span>
                                &nbsp;
                                <span class="badge badge-primary">menor 60 dias</span>
                                &nbsp;
                                <span class="badge badge-warning">menor 90 dias</span>
                                &nbsp;
                                <span class="badge badge-danger">maior 90 dias</span>

                              </div>

                          </div>
                        </form>

                        <hr />


            		<div class="table-responsive tabela-dados">
            			<table class="table table-striped table-bordered table-hover table-sm table-condensed">
                            <thead>
                                <tr>
                                    <th style="min-width: 150px" class="acoes"></th>
                                    <th>Imagem</th>
                                    <th>Nome</th>
                                    <th>Tags</th>
                                    <th>Contatos</th>
                                    <th>CNPJ</th>
                                    <th>Configuração</th>
                                    <th>Pontuação</th>
                                    <th>Último ponto</th>
                                    <th>Ativo</th>
                                    <th>Criado</th>
                                </tr>
                            </thead>
                            @foreach($dados as $dado)
                            <tr>
                                <td>
                                    <a href="{{route('clientes-apagar',$dado->id)}}" class="btn btn-sm btn-danger apagar" title="Excluir"><i class="fa fa-trash"></i></a>
                                    <a href="{{route('clientes',['id'=>$dado->id, 'page'=>$page])}}" class="btn btn-sm btn-warning editar" title="Editar"><i class="fa fa-edit"></i></a>

                                    <form action="{{route('clientes-imagem')}}" method="post" role="form" class="form form-inline d-inline" enctype="multipart/form-data">
                                        {{ csrf_field() }}
                                        <input type="hidden" name="clientes_id" value="{{$dado->id}}" />
                                          <a href="#" class="btn btn-sm btn-info" onclick="$('#input-foto{{$dado->id}}').click(); return false;"><i class="fa fa-image fa-fw"></i></a>
                                          <input type="file" name="imagem" class="form-control" style="display: none;" id="input-foto{{$dado->id}}" onchange="if($(this)[0].files[0].size > 2097152) {alert('Arquivo ultrapassou limite de 2Mb'); return false;} $(this).parent('form').submit();" />
                                    </form>

                                </td>
                                <td>
                                    @if($dado->imagem != '')
                                        <a href="{{url($dado->imagem)}}" target="_blank" data-lightbox="{{$dado->id}}">
                                            <img src="{{url($dado->imagem)}}" class="img-fluid" style="max-height: 100px; max-width:150px" />
                                        </a>
                                        <a href="{{route('clientes-imagem-excluir', $dado->id)}}" class="text-secondary f-left"><small><i class="fa fa-trash"></i></small></a>
                                    @endif
                                </td>
                                <td>
                                    {{$dado->nome}}
                                    <br />
                                    <a href="{{route('cliente_app',['codigo'=>$dado->url])}}" target="_blank"><i class="fa fa-link"></i></a>
                                    /{{$dado->url}}
                                    <br />
                                    {{$dado->categoria}}
                                </td>
                                <td>
                                    @if($dado->tags)
                                        @php
                                            $tags = explode(',', $dado->tags);
                                            $tagColors = [
                                                'vip' => 'badge-warning',
                                                'aplicativo' => 'badge-info',
                                                'comum' => 'badge-secondary',
                                                'premium' => 'badge-success',
                                                'novo' => 'badge-primary',
                                                'inativo' => 'badge-danger'
                                            ];
                                        @endphp
                                        @foreach($tags as $tag)
                                            @php
                                                $tag = trim(strtolower($tag));
                                                $badgeClass = $tagColors[$tag] ?? 'badge-light';
                                            @endphp
                                            <span class="badge {{$badgeClass}} mr-1 mb-1">{{ucfirst($tag)}}</span>
                                        @endforeach
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    {{$dado->email}}
                                    <br />
                                    {{$dado->celular}}
                                </td>
                                <td>{{$dado->cnpj}}</td>
                                <td>
                                    Conversão: {{$dado->conversao}}
                                    <br />
                                    Dias de validade: {{$dado->diasvalidade}}
                                    <br />
                                    Pontos para resgate: {{$dado->reaiscashback}}
                                    <br />
                                    Prêmio do cashback:
                                    @if($dado->premiocashback)
                                    [{{number_format($dado->premiocashback['pontos'],0,'','')}}] {{$dado->premiocashback['titulo']}}
                                    @endif
                                </td>
                                <td>{{number_format($dado->pontuacao(),0,',','')}}</td>
                                <td>
                                    @if(!empty($dado->pontos()->first()))

                                    @php
                                    $created_at = $dado->pontos()->first()->created_at;
                                    $dias = \Carbon\Carbon::parse(\Carbon\Carbon::now())->diffInDays($created_at);
                                    @endphp

                                    {{date('d/m/Y H:i', strtotime($created_at))}}

                                    @if($dias <= 30)
                                    <span class="badge badge-success">{{$dias}}</span>
                                    @elseif($dias <= 60)
                                    <span class="badge badge-primary">{{$dias}}</span>
                                    @elseif($dias <= 90)
                                    <span class="badge badge-warning">{{$dias}}</span>
                                    @else
                                    <span class="badge badge-danger">{{$dias}}</span>
                                    @endif

                                    @else
                                    <em>-----</em>
                                    @endif
                                </td>
                                <td>

                                <input id="ativo_clientes{{$dado->id}}" type="text" name="ativo_clientes" data-url="{{route('clientes-atualizar', array('id'=>$dado->id, 'campo'=>'ativo'))}}" class="form-control form-control-sm atualizar-item" value="{{$dado->ativo}}" style="width: 50px; display:none" />
                                <div class="atualizar-item-load"></div>

                                    <a onclick="$('#ativo_clientes{{$dado->id}}').val($('#ativo_clientes{{$dado->id}}').val() == 1?0:1); $('#ativo_clientes{{$dado->id}}').blur(); var_atual = $('#ativo_clientes{{$dado->id}}').val(); $(this).html($('#botao_'+var_atual).html());">
                                    @if($dado->ativo==1)
                                        <button class="btn btn-sm btn-light">Ativo</button>
                                    @else
                                        <button class="btn btn-sm btn-dark">Inativo</button>
                                    @endif
                                    </a>
                                </td>
                                <td>
                                    {{$dado->created_at->format('d/m/Y')}}
                                </td>

                            </tr>
                            @endforeach
                            <div id="botao_1" style="display: none;"><button class="btn btn-sm btn-light">Ativo</button></div>
                            <div id="botao_0" style="display: none;"><button class="btn btn-sm btn-dark">Inativo</button></div>


                        </table>

            			<div class="pagination pagination-sm">
            				<div>{{ $dados->links() }}</div>
            				<div class="btn-sm">Exibindo de {{ $dados->firstItem() }} até {{ $dados->lastItem() }} de {{ $dados->total() }} registros.</div>
            			</div>

                    </div>

                </div>

                </div>

@stop
