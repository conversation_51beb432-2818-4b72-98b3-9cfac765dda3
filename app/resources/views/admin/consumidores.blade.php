@extends('layouts.admin')

@section('content')
<style>
/* Cards de Estatísticas */
.border-left-success { border-left: 0.25rem solid #28a745 !important; }
.border-left-primary { border-left: 0.25rem solid #435887 !important; }
.border-left-warning { border-left: 0.25rem solid #ffc107 !important; }
.border-left-danger { border-left: 0.25rem solid #dc3545 !important; }
.border-left-secondary { border-left: 0.25rem solid #6c757d !important; }
.border-left-info { border-left: 0.25rem solid #17a2b8 !important; }

.text-xs { font-size: 0.75rem !important; }
.text-gray-800 { color: #5a5c69 !important; }
.py-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }
.h-100 { height: 100% !important; }
.no-gutters { margin-right: 0; margin-left: 0; }
.no-gutters > .col, .no-gutters > [class*="col-"] { padding-right: 0; padding-left: 0; }
.mr-2 { margin-right: 0.5rem !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-0 { margin-bottom: 0 !important; }
.font-weight-bold { font-weight: 700 !important; }
.text-uppercase { text-transform: uppercase !important; }
.h5 { font-size: 1.25rem; font-weight: 500; line-height: 1.2; margin-bottom: 0.5rem; }
.align-items-center { align-items: center !important; }
.col-auto { flex: 0 0 auto; width: auto; max-width: 100%; }

.card:hover { transform: translateY(-2px); transition: all 0.3s ease; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important; }

@media (max-width: 1200px) {
    .col-md-2 { flex: 0 0 50%; max-width: 50%; }
}
@media (max-width: 576px) {
    .col-md-2 { flex: 0 0 100%; max-width: 100%; margin-bottom: 1rem; }
}
</style>
    <div class="bg-white rounded shadow p-3 mb-5">

        <div>
            <h1><i class="fa fa-users"></i> Consumidores</h1>

            @if ($permissao == 'gestor' or !empty($registro->id))
                <form action="{{ route('consumidores-salvar') }}" method="POST" id="form-consumidores" role="form"
                    class="form form-inline formloading">
                    {{ csrf_field() }}

                    <div class="form-row">
                        <div class="form-group col-auto">
                            @if (!empty($registro->id))
                                <div class="btn btn-sm btn-secondary"><i class="fa fa-edit"></i> Alterar registro</div>
                                <input type="hidden" name="id" value="{{ $registro->id }}" />
                            @else
                                <div class="btn btn-sm btn-light"><i class="fa fa-save"></i> Adicionar registro</div>
                            @endif
                        </div>

                        <div class="form-group col-auto">
                            <input type="text" name="nome" class="form-control form-control-sm" id="nome"
                                placeholder="Nome" value="{{ $registro->nome }}" required="required" />
                        </div>
                        <div class="form-group col-auto">
                            <input type="email" name="email" class="form-control form-control-sm" id="email"
                                placeholder="Email" value="{{ $registro->email }}" />
                        </div>
                        <div class="form-group col-auto">
                            <input type="text" name="celular" class="form-control form-control-sm telefone"
                                id="celular" placeholder="Celular" value="{{ $registro->celular }}" required="required" />
                        </div>
                        <div class="form-group col-auto">
                            <input type="password" name="senha" class="form-control form-control-sm" id="senha"
                                placeholder="Senha (preencha se for alterar)" />
                        </div>

                        <div class="form-group col-auto">

                            <select name="tipopessoa" class="form-control form-control-sm">
                                <option value="">-- Tipo Pessoa --</option>
                                <option value="fisica" @if ($registro->tipopessoa == 'fisica') selected="selected" @endif>Física
                                </option>
                                <option value="juridica" @if ($registro->tipopessoa == 'juridica') selected="selected" @endif>
                                    Jurídica</option>
                            </select>

                        </div>
                        <div class="form-group col-auto">
                            <input type="text" name="cadastronacional" class="form-control form-control-sm"
                                id="cadastronacional" placeholder="Cadastro Nacional"
                                value="{{ $registro->cadastronacional }}" readonly="readonly" />
                        </div>
                        <div class="form-group col-auto">
                            <input type="date" name="nascimento" class="form-control form-control-sm" id="nascimento"
                                value="{{ $registro->nascimento ? $registro->nascimento->format('Y-m-d') : null }}" />
                        </div>


                        <div class="form-group col-auto input-loading">
                            <input type="submit" class="btn btn-sm btn-success" value="Salvar" />
                            @if (!empty($registro->id))
                                &nbsp;<a href="{{ route('consumidores', ['', 'page' => $page]) }}"
                                    class="btn btn-sm btn-secondary editar">Cancelar</a>
                            @endif
                        </div>
                    </div>

                </form>


                <hr />
            @endif

            <!--
                                        ***
                                        *** FILTRO
                                        ***
                                        -->
            <form action="{{ route('consumidores-filtrar') }}" method="POST" id="form-filtro-consumidores" role="form"
                class="form form-inline formloading">
                {{ csrf_field() }}

                <div class="form-row">
                    <div class="form-group col-auto">
                        <div class="btn btn-sm btn-light"><i class="fa fa-filter"></i> Filtrar</div>
                    </div>

                    @if (Session::get('logado') == 'gestor')
                        <div class="form-group col-auto">
                            <select name="clientes_id" class="form-control form-control-sm">
                                <option value="">-- Todos Clientes --</option>
                                @foreach ($clientes as $cliente)
                                    <option value="{{ $cliente->id }}"
                                        @if ($clientes_id == $cliente->id) selected="selected" @endif>
                                        {{ $cliente->nome }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    @endif

                    <div class="form-group col-auto">
                        <input type="text" name="termo" class="form-control form-control-sm" placeholder="Termo"
                            id="termo" value="{{ $termo }}" />
                    </div>

                    @if (!empty($unidades) && count($unidades) > 0)
                        <div class="form-group col-auto">
                            <select name="unidade_id" id="unidade_id" class="form-control form-control-sm">
                                <option value="">-- Selecione uma unidade --</option>
                                @foreach ($unidades as $unidade)
                                    <option @if ($unidade_id == $unidade->id) selected @endif value="{{ $unidade->id }}">
                                        {{ $unidade->nome }}</option>
                                @endforeach
                            </select>
                        </div>
                    @endif

                    <div class="form-group col-auto input-loading">
                        <input type="submit" class="btn btn-sm btn-secondary" value="Filtrar" />
                    </div>

                    <div class="form-group col-auto">

                        <span class="badge badge-success">menor 30 dias</span>
                        &nbsp;
                        <span class="badge badge-primary">menor 60 dias</span>
                        &nbsp;
                        <span class="badge badge-warning">menor 90 dias</span>
                        &nbsp;
                        <span class="badge badge-danger">maior 90 dias</span>

                    </div>


                </div>
            </form>

            <!-- Resumo de Inatividade -->
            @if(isset($estatisticas_inatividade))
            <hr />
            <div class="row mb-4">
                <div class="col-12">
                    <h5><i class="fa fa-chart-bar"></i> Resumo de Atividade dos Consumidores</h5>
                    <p class="text-muted small">
                        Baseado no último acesso ou último ponto registrado (o que for mais recente)
                    </p>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Ativos (≤ 30 dias)
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ $estatisticas_inatividade['ativo_30_dias'] }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Moderados (31-60 dias)
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ $estatisticas_inatividade['ativo_60_dias'] }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Atenção (61-90 dias)
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ $estatisticas_inatividade['ativo_90_dias'] }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card border-left-danger shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                        Inativos (> 90 dias)
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ $estatisticas_inatividade['inativo_90_dias'] }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-times-circle fa-2x text-danger"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card border-left-secondary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                        Sem Acesso
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ $estatisticas_inatividade['sem_acesso'] }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-slash fa-2x text-secondary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Total
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ $estatisticas_inatividade['total'] }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-info"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <hr />
            <form action="{{ route('consumidores-exportar') }}" method="POST" id="form-consumidores-exportar"
                role="form" class="form form-inline">
                {{ csrf_field() }}
                <input type="hidden" name="clientes_id" value="{{ $clientes_id }}" />
                <input type="hidden" name="termo" value="{{ $termo }}" />
                <button type="submit" class="btn btn-sm btn-light"><i class="fa fa-save"></i> Exportar</button>
            </form>


            <hr />

            <div class="table-responsive tabela-dados">
                <table class="table table-striped table-bordered table-hover table-sm table-condensed">
                    <thead>
                        <tr>
                            <th class="acoes" style="width: 120px"></th>
                            @if (Session::get('logado') == 'gestor')
                                <th>Cliente</th>
                            @endif
                            <th>Nome</th>
                            <th>Email</th>
                            <th>Telefone</th>
                            <th>Tipo</th>
                            <th>Pontos</th>
                            <th>Último ponto</th>
                            <th>Último acesso</th>
                            <th>Ativo</th>
                            <th>Criado</th>
                        </tr>
                    </thead>
                    @foreach ($dados as $dado)
                        <tr>
                            <td>
                                <a href="{{ route('consumidores-apagar', $dado->id) }}"
                                    class="btn btn-sm btn-danger apagar" title="Excluir"><i class="fa fa-trash"></i></a>
                                <a href="{{ route('consumidores', ['id' => $dado->id, 'page' => $page]) }}"
                                    class="btn btn-sm btn-warning editar" title="Editar"><i class="fa fa-edit"></i></a>
                                @if (Session::get('logado') == 'cliente')
                                    <a href="{{ route('consumidores-notificar', [$dado->id] + $filtros) }}"
                                        class="btn btn-sm btn-success editar" title="Notificar"><i
                                            class="fa fa-share"></i></a>
                                @endif
                            </td>
                            @if (Session::get('logado') == 'gestor')
                                <td>
                                    @foreach ($dado->cadastros as $cadastro)
                                        {{ $cadastro->cliente->nome }}<br />
                                    @endforeach
                                </td>
                            @endif
                            <td>
                                {{ $dado->nome }}
                                <br>
                                <small>{{ $dado->atuacao }}</small>
                            </td>
                            <td>{{ $dado->email }}</td>
                            <td>
                                <a href="https://wa.me/+55{{ $dado->celular2numero() }}" target="_blank"
                                    class="btn btn-sm btn-info"><i class="fab fa-whatsapp"></i></a>
                                {{ $dado->celular }}
                            </td>
                            <td>
                                {{ $dado->tipopessoa }}
                                <br>
                                {{ $dado->cadastronacional }}
                                @if ($dado->tipopessoa == 'fisica')
                                    @if ($dado->nascimento != '')
                                        <br>{{ $dado->nascimento->format('d/m/Y') }}
                                    @endif
                                @endif
                            </td>
                            <td>
                                @if (Session::get('logado') == 'gestor')
                                    @foreach ($dado->cadastros as $cadastro)
                                        {{ number_format($dado->pontuacao($cadastro->cliente->id), 0) }}<br />
                                    @endforeach
                                @else
                                    {{ number_format($dado->pontuacao($clientes_id), 0) }}<br />
                                @endif

                            </td>
                            <td>
                                @if (!empty($dado->pontos($clientes_id)->first()))
                                    @php
                                        $created_at = $dado->pontos($clientes_id)->first()->created_at;
                                        $dias = \Carbon\Carbon::parse(\Carbon\Carbon::now())->diffInDays($created_at);
                                    @endphp

                                    {{ date('d/m/Y H:i', strtotime($created_at)) }}

                                    @if ($dias <= 30)
                                        <span class="badge badge-success">{{ $dias }}</span>
                                    @elseif($dias <= 60)
                                        <span class="badge badge-primary">{{ $dias }}</span>
                                    @elseif($dias <= 90)
                                        <span class="badge badge-warning">{{ $dias }}</span>
                                    @else
                                        <span class="badge badge-danger">{{ $dias }}</span>
                                    @endif
                                @else
                                    <em>-----</em>
                                @endif
                            </td>
                            <td>
                                @if (!empty($dado->acessos()->first()))
                                    @php
                                        $created_at = $dado->acessos()->first()->created_at;
                                        $dias = \Carbon\Carbon::parse(\Carbon\Carbon::now())->diffInDays($created_at);
                                    @endphp

                                    {{ date('d/m/Y H:i', strtotime($created_at)) }}

                                    @if ($dias <= 30)
                                        <span class="badge badge-success">{{ $dias }}</span>
                                    @elseif($dias <= 60)
                                        <span class="badge badge-primary">{{ $dias }}</span>
                                    @elseif($dias <= 90)
                                        <span class="badge badge-warning">{{ $dias }}</span>
                                    @else
                                        <span class="badge badge-danger">{{ $dias }}</span>
                                    @endif
                                @else
                                    <em>-----</em>
                                @endif
                            </td>
                            <td>

                                <input id="ativo_consumidores{{ $dado->id }}" type="text"
                                    name="ativo_consumidores"
                                    data-url="{{ route('consumidores-atualizar', ['id' => $dado->id, 'campo' => 'ativo']) }}"
                                    class="form-control form-control-sm atualizar-item" value="{{ $dado->ativo }}"
                                    style="width: 50px; display:none" />
                                <div class="atualizar-item-load"></div>

                                <a
                                    onclick="$('#ativo_consumidores{{ $dado->id }}').val($('#ativo_consumidores{{ $dado->id }}').val() == 1?0:1); $('#ativo_consumidores{{ $dado->id }}').blur(); var_atual = $('#ativo_consumidores{{ $dado->id }}').val(); $(this).html($('#botao_'+var_atual).html());">
                                    @if ($dado->ativo == 1)
                                        <button class="btn btn-sm btn-light">Ativo</button>
                                    @else
                                        <button class="btn btn-sm btn-dark">Inativo</button>
                                    @endif
                                </a>
                            </td>
                            <td>
                                {{ $dado->created_at->format('d/m/Y') }}
                            </td>

                        </tr>
                    @endforeach
                    <div id="botao_1" style="display: none;"><button class="btn btn-sm btn-light">Ativo</button></div>
                    <div id="botao_0" style="display: none;"><button class="btn btn-sm btn-dark">Inativo</button></div>


                </table>

                <div class="pagination pagination-sm">
                    <div>{{ $dados->links() }}</div>
                    <div class="btn-sm">Exibindo de {{ $dados->firstItem() }} até {{ $dados->lastItem() }} de
                        {{ $dados->total() }} registros.</div>
                </div>

            </div>

        </div>

    </div>

@stop
