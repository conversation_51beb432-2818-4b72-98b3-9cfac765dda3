@extends('layouts.app')



@section('content')


    <style>
        body, .meubgcor, .meubotao:hover, .meubotao2:hover {
            background-color: {{!empty($cliente ?? ''->bg_color) ? $cliente ?? ''->bg_color." !important" : '' }};
        }
        .meubgcor2, .meubotao {
            background-color: {{!empty($cliente ?? ''->bg_color2) ? $cliente ?? ''->bg_color2." !important" : '' }};
        }
        .minhacor, .meubotao2 {
            color: {{!empty($cliente ?? ''->bg_color) ? $cliente ?? ''->bg_color." !important" : '' }};
        }
        .textocor, .texto a:link, .texto a:visited, .texto a:hover {
            color: {{!empty($cliente ?? ''->bg_color2) ? $cliente ?? ''->bg_color2." !important" : '' }};
        }
        .meubotao2 {
            border-color: {{!empty($cliente ?? ''->bg_color2) ? $cliente ?? ''->bg_color2." !important" : '' }};
        }
    </style>


@if(!empty($cliente))
<div class="container-fluid">
    <h4 class="text-left">
        @if(!empty($cliente->imagem))
            <div class="text-center">
                <img src="{{ url($cliente->imagem) }}" class="img-responsive mb-3" style="max-width:300px; max-height:120px" />
            </div>
        @endif
        <!--
        <small>{{ $cliente->nome ?? '' }}</small>
        -->
    </h4>
</div>
@endif



<div class="container container-fluid p-3 d-flex justify-content-center">


    <div id="login" class="text-center bg-white rounded shadow p-4">

        <svg width="39" height="39" viewBox="0 0 39 39" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.2687 0.438109C9.8331 1.61815 4.64762 5.67123 2.18359 10.6663C-0.727863 16.5681 -0.727863 22.4246 2.18359 28.3263C4.67995 33.3867 9.84834 37.3889 15.409 38.5674C29.1815 41.4865 41.4852 29.1762 38.5676 15.3963C36.3619 4.97914 25.7467 -1.83591 15.2687 0.438109ZM22.2414 11.7097C22.8108 12.0041 23.3988 12.6541 23.6584 13.2757C24.2958 14.8021 23.8179 16.1723 21.9632 18.1341C21.1059 19.041 20.4218 20.0529 20.4218 20.4136C20.4218 21.4753 19.846 22.2427 19.0493 22.2427C17.1105 22.2427 17.4616 19.2049 19.6419 17.1143C20.6612 16.1369 21.0318 15.5476 21.0318 14.9034C21.0318 13.2062 18.8599 12.8534 17.3719 14.3087C16.2998 15.3572 16.0738 15.4075 15.4111 14.7447C14.8042 14.1372 14.8097 13.847 15.4501 12.7621C16.4844 11.0106 19.875 10.4851 22.2414 11.7097ZM20.2519 24.4469C20.8598 25.3151 20.8522 25.8707 20.222 26.5671C19.2775 27.6114 17.3719 26.8979 17.3719 25.5C17.3719 23.9394 19.3824 23.2043 20.2519 24.4469Z" fill="{{!empty($cliente ?? ''->bg_color) ? $cliente ?? ''->bg_color : '#435887' }}"/>
            <circle cx="19.5" cy="19.5" r="13.5" fill="{{!empty($cliente ?? ''->bg_color) ? $cliente ?? ''->bg_color : '#435887' }}"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.4368 3.5155C12.4166 4.30538 10.2783 5.55493 7.92195 7.90669C5.53197 10.2923 4.30718 12.4053 3.50979 15.5164C0.869554 25.8218 8.82474 36 19.5198 36C31.8104 36 39.6972 23.1674 34.2412 12.0473C32.8448 9.20114 29.8205 6.18233 26.9695 4.78901C23.2449 2.9688 19.2251 2.52483 15.4368 3.5155ZM22.8051 5.58606C30.4099 7.37542 35.2398 15.1531 33.4505 22.7281C33.0694 24.3417 32.2287 26.3338 31.5958 27.1226C31.3894 27.3801 31.015 27.1928 30.0622 26.3558C27.5329 24.1335 23.1025 22.5226 19.5198 22.5226C15.937 22.5226 11.5066 24.1335 8.97733 26.3558C8.02459 27.1928 7.65017 27.3801 7.44379 27.1226C6.81084 26.3338 5.97012 24.3417 5.58909 22.7281C3.17124 12.4918 12.5339 3.1693 22.8051 5.58606ZM17.3125 7.72519C15.9583 8.15126 13.6486 10.5385 13.2333 11.9415C12.469 14.5238 13.0079 16.5825 14.9578 18.5289C17.7818 21.3477 21.6785 21.2877 24.2928 18.3851C26.8586 15.5365 26.778 12.1128 24.0818 9.42147C22.1401 7.48366 19.8804 6.91741 17.3125 7.72519Z" fill="white"/>
        </svg>
        <h2 style="padding: 15px 0 0 0">Faça seu login</h2>
        <div style="padding:10px 20% 20px; font-size: 20px;">
        Acesse e acompanhe seus <span class="textocor">pontos.</span>
        </div>

        <form action="{{route('verificalogin_app')}}" method="POST" id="form-login" role="form" class="form">
            {{ csrf_field() }}

            <div class="form-group">
            <input type="text" name="cadastronacional" class="form-control form-control-lg" id="cadastronacional" placeholder="CPF/CNPJ:" required="required" pattern="[0-9]*" />
            </div>
            <div class="form-group">
            <input type="password" name="senha" class="form-control form-control-lg"  id="senha" placeholder="Senha:" required="required" />
            </div>

            <div class="form-group small text-right">
                <a href="#" onclick="$('#login').hide(); $('#novasenha').fadeIn(); return false;" class="text-black-50"> Esqueceu sua senha <span class="textocor">Clique aqui.</span></a>
            </div>

            @if(!empty($cliente))
            <div class="form-group">
                <input type="hidden" name="url" value="{{$cliente ?? ''->url}}" />
                <button type="submit" class="btn meubotao">
                    Acessar &nbsp; <svg width="39" height="16" viewBox="0 0 39 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M38.004 8.70711C38.3945 8.31658 38.3945 7.68342 38.004 7.29289L31.64 0.928932C31.2495 0.538408 30.6163 0.538408 30.2258 0.928932C29.8353 1.31946 29.8353 1.95262 30.2258 2.34315L35.8827 8L30.2258 13.6569C29.8353 14.0474 29.8353 14.6805 30.2258 15.0711C30.6163 15.4616 31.2495 15.4616 31.64 15.0711L38.004 8.70711ZM0 9H37.2969V7H0V9Z" fill="white"/>
                </svg>
                </button>
            </div>
            <div>
                <a href="{{route('cadastrar_app', ['codigo'=>$cliente ?? ''->url])}}" class="btn meubotao2">Cadastre-se</a>
            </div>
            @endif

        </form>


        <img src="{{url('img/moedas.png')}}" style="width: 30%; float: right; margin: 0 -10% -10% 0;" />

    </div>



    <div id="novasenha" class="text-center bg-white rounded shadow p-4" style="border: 1px solid #666; display:none">
        <img src="{{url('img/moeda.png')}}" style="width: 15%; float: left; margin: -15% 0 0 -10%;" />

        <svg width="39" height="39" viewBox="0 0 39 39" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.2687 0.438109C9.8331 1.61815 4.64762 5.67123 2.18359 10.6663C-0.727863 16.5681 -0.727863 22.4246 2.18359 28.3263C4.67995 33.3867 9.84834 37.3889 15.409 38.5674C29.1815 41.4865 41.4852 29.1762 38.5676 15.3963C36.3619 4.97914 25.7467 -1.83591 15.2687 0.438109ZM22.2414 11.7097C22.8108 12.0041 23.3988 12.6541 23.6584 13.2757C24.2958 14.8021 23.8179 16.1723 21.9632 18.1341C21.1059 19.041 20.4218 20.0529 20.4218 20.4136C20.4218 21.4753 19.846 22.2427 19.0493 22.2427C17.1105 22.2427 17.4616 19.2049 19.6419 17.1143C20.6612 16.1369 21.0318 15.5476 21.0318 14.9034C21.0318 13.2062 18.8599 12.8534 17.3719 14.3087C16.2998 15.3572 16.0738 15.4075 15.4111 14.7447C14.8042 14.1372 14.8097 13.847 15.4501 12.7621C16.4844 11.0106 19.875 10.4851 22.2414 11.7097ZM20.2519 24.4469C20.8598 25.3151 20.8522 25.8707 20.222 26.5671C19.2775 27.6114 17.3719 26.8979 17.3719 25.5C17.3719 23.9394 19.3824 23.2043 20.2519 24.4469Z" fill="#3F458C"/>
            <circle cx="19.5" cy="19.5" r="13.5" fill="#3F458C"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.4368 3.5155C12.4166 4.30538 10.2783 5.55493 7.92195 7.90669C5.53197 10.2923 4.30718 12.4053 3.50979 15.5164C0.869554 25.8218 8.82474 36 19.5198 36C31.8104 36 39.6972 23.1674 34.2412 12.0473C32.8448 9.20114 29.8205 6.18233 26.9695 4.78901C23.2449 2.9688 19.2251 2.52483 15.4368 3.5155ZM22.8051 5.58606C30.4099 7.37542 35.2398 15.1531 33.4505 22.7281C33.0694 24.3417 32.2287 26.3338 31.5958 27.1226C31.3894 27.3801 31.015 27.1928 30.0622 26.3558C27.5329 24.1335 23.1025 22.5226 19.5198 22.5226C15.937 22.5226 11.5066 24.1335 8.97733 26.3558C8.02459 27.1928 7.65017 27.3801 7.44379 27.1226C6.81084 26.3338 5.97012 24.3417 5.58909 22.7281C3.17124 12.4918 12.5339 3.1693 22.8051 5.58606ZM17.3125 7.72519C15.9583 8.15126 13.6486 10.5385 13.2333 11.9415C12.469 14.5238 13.0079 16.5825 14.9578 18.5289C17.7818 21.3477 21.6785 21.2877 24.2928 18.3851C26.8586 15.5365 26.778 12.1128 24.0818 9.42147C22.1401 7.48366 19.8804 6.91741 17.3125 7.72519Z" fill="white"/>
        </svg>

        <h2 style="padding: 15px 0 0 0">Nova Senha</h2>
        <div style="padding:10px 20% 20px; font-size: 20px;">
            Informe seu email para receber o link e criar uma <span class="textocor">nova senha.</span>
        </div>


        <form action="{{route('novasenha_app')}}" method="POST" id="form-login-nova-senha" role="form" class="form">
            {{ csrf_field() }}

            <div class="form-group">
            <input type="email" name="email" class="form-control form-control-lg" id="email" placeholder="Email:" required="required" />
            </div>
            <div class="form-group">
            <input type="submit" class="form-control btn meubotao" value="Receber link" />
            </div>
            <div class="form-group small text-right">
                <a href="#" onclick="$('#novasenha').hide(); $('#login').fadeIn(); return false;" class="text-black-50">Faça seu <span class="textocor">login</span></a>
            </div>
        </form>

        <img src="{{url('img/moedas.png')}}" style="width: 30%; float: right; margin: 0 -10% -10% 0;" />


    </div>

    @if(isset($novasenhatoken))
    <div id="novasenhaform" class="text-center bg-white rounded shadow p-4" style="border: 1px solid #666; display:none">
        <img src="{{url('img/moeda.png')}}" style="width: 15%; float: left; margin: -15% 0 0 -10%;" />

        <svg width="39" height="39" viewBox="0 0 39 39" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.2687 0.438109C9.8331 1.61815 4.64762 5.67123 2.18359 10.6663C-0.727863 16.5681 -0.727863 22.4246 2.18359 28.3263C4.67995 33.3867 9.84834 37.3889 15.409 38.5674C29.1815 41.4865 41.4852 29.1762 38.5676 15.3963C36.3619 4.97914 25.7467 -1.83591 15.2687 0.438109ZM22.2414 11.7097C22.8108 12.0041 23.3988 12.6541 23.6584 13.2757C24.2958 14.8021 23.8179 16.1723 21.9632 18.1341C21.1059 19.041 20.4218 20.0529 20.4218 20.4136C20.4218 21.4753 19.846 22.2427 19.0493 22.2427C17.1105 22.2427 17.4616 19.2049 19.6419 17.1143C20.6612 16.1369 21.0318 15.5476 21.0318 14.9034C21.0318 13.2062 18.8599 12.8534 17.3719 14.3087C16.2998 15.3572 16.0738 15.4075 15.4111 14.7447C14.8042 14.1372 14.8097 13.847 15.4501 12.7621C16.4844 11.0106 19.875 10.4851 22.2414 11.7097ZM20.2519 24.4469C20.8598 25.3151 20.8522 25.8707 20.222 26.5671C19.2775 27.6114 17.3719 26.8979 17.3719 25.5C17.3719 23.9394 19.3824 23.2043 20.2519 24.4469Z" fill="#3F458C"/>
            <circle cx="19.5" cy="19.5" r="13.5" fill="#3F458C"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.4368 3.5155C12.4166 4.30538 10.2783 5.55493 7.92195 7.90669C5.53197 10.2923 4.30718 12.4053 3.50979 15.5164C0.869554 25.8218 8.82474 36 19.5198 36C31.8104 36 39.6972 23.1674 34.2412 12.0473C32.8448 9.20114 29.8205 6.18233 26.9695 4.78901C23.2449 2.9688 19.2251 2.52483 15.4368 3.5155ZM22.8051 5.58606C30.4099 7.37542 35.2398 15.1531 33.4505 22.7281C33.0694 24.3417 32.2287 26.3338 31.5958 27.1226C31.3894 27.3801 31.015 27.1928 30.0622 26.3558C27.5329 24.1335 23.1025 22.5226 19.5198 22.5226C15.937 22.5226 11.5066 24.1335 8.97733 26.3558C8.02459 27.1928 7.65017 27.3801 7.44379 27.1226C6.81084 26.3338 5.97012 24.3417 5.58909 22.7281C3.17124 12.4918 12.5339 3.1693 22.8051 5.58606ZM17.3125 7.72519C15.9583 8.15126 13.6486 10.5385 13.2333 11.9415C12.469 14.5238 13.0079 16.5825 14.9578 18.5289C17.7818 21.3477 21.6785 21.2877 24.2928 18.3851C26.8586 15.5365 26.778 12.1128 24.0818 9.42147C22.1401 7.48366 19.8804 6.91741 17.3125 7.72519Z" fill="white"/>
        </svg>

        <h2 style="padding: 15px 0 0 0">Nova Senha</h2>
        <div style="padding:10px 20% 20px; font-size: 20px;">
            Confira seu email e preencha a <span class="textocor">nova senha.</span>
        </div>

        <div class="p-3">{{$usuario->email}}</div>
        <form action="{{route('novasenhaenviado_app')}}" method="POST" id="form-login" role="form" class="form">
            {{ csrf_field() }}

            <div class="form-group">
                <input type="password" name="senha" class="form-control form-control-lg"  id="senha" placeholder="Senha:" required="required" />
            </div>
            <div class="form-group">
                <input type="hidden" name="token" value="{{$usuario->token}}" />
                <input type="hidden" name="tipousuario" value="{{$tipousuario}}" />
                <input type="submit" class="form-control btn btn-dark" value="Salvar" />
            </div>
        </form>


    </div>
    <script>
        $('#login').hide();
        $('#novasenha').hide();
        $('#novasenhaform').show();

    </script>
    @endif



</div>


<script>
    function fetch_login_auto() {
        fetch('/login_auto?url={{ $cliente ?? ''->url }}')
            .then(response => {
                if (response.status == 200) {
                    window.location.href = '/home';
                }
            });
    }

    fetch_login_auto();

    $(document).ready(function () {
        $('.flexslider').flexslider({
            animation: "slide",
            controlNav: true,
            directionNav: false
        });
    });
</script>



@if(isset($propagandas) and count($propagandas) > 0)
<div class="container text-center boxeshome" id="propagandas">
<div class="flexslider">
<ul class="slides">
    @foreach($propagandas as $propaganda)
                <li>
                    <a href="{{$propaganda->link}}" target="_blank">
                        <img src="{{url($propaganda->imagem)}}" class="img-fluid rounded-lg" />
                    </a>
                </li>
    @endforeach
</ul>
</div>
</div>


<style>
.flex-control-nav {
  text-align: center;
  margin-top: 20px;
}

.flex-control-nav li {
  display: inline-block;
  margin: 0 5px;
}

.flex-control-nav li a {
  background: #ccc;
  width: 10px;
  height: 10px;
  display: block;
  border-radius: 50%;
  cursor: pointer;
}

.flex-control-nav li a.flex-active {
  background: #333;
}
.flex-control-nav li a {
  text-indent: -9999px; /* Esconde o texto (números) dentro dos pontos */
}
</style>

@endif



@stop
