<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ForceHttp
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Se FORCE_HTTPS for false, garantir que a requisição seja tratada como HTTP
        if (env('FORCE_HTTPS', false) === false) {
            // Remover headers que possam forçar HTTPS
            $request->server->set('HTTPS', 'off');
            $request->server->set('SERVER_PORT', '80');
            $request->server->remove('HTTP_X_FORWARDED_PROTO');
            
            // Se a requisição veio como HTTPS, redirecionar para HTTP
            if ($request->isSecure()) {
                $url = str_replace('https://', 'http://', $request->fullUrl());
                return redirect($url, 301);
            }
        }

        return $next($request);
    }
}
