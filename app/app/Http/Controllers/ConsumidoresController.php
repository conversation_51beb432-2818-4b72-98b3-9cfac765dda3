<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ConsumidoresExport;

use App\Consumidores;
use App\Clientes;
use App\Cadastros;
use App\Acessos;
use App\Notificacoes;
use App\Unidades;

class ConsumidoresController extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id = null)
    {


        $registro = new Consumidores;
        if($id) {
            $registro = Consumidores::findOrFail($id);
        }
        if($request->session()->has('registro')) {
            $registro = new Consumidores($request->session()->get('registro'));
        }

        $unidade_id = $request->get('unidade_id');

        $dados = Consumidores::select('consumidores.*')
                    ->orderBy('created_at', 'DESC');


        $clientes_id = $request->get('clientes_id',null);

        if($request->session()->get('logado') == 'cliente') {
            $clientes_id = $request->session()->get('logado_usuario')->clientes_id;
        }
        if($clientes_id) {
            $dados->join('cadastros','cadastros.consumidores_id','=','consumidores.id');
            $dados->where('cadastros.clientes_id','=',$clientes_id);
        }

        if(!empty($unidade_id)) {
            $dados->where('consumidores.unidade_id','=', $unidade_id);
        }

        $termo = $request->get('termo',null);
        if($termo) {
            $dados->whereRaw('(consumidores.nome LIKE "%'.$termo.'%" OR consumidores.email LIKE "%'.$termo.'%")');
        }

        $total = $dados->count();

        // Calcular estatísticas de inatividade de forma otimizada
        $estatisticasInatividade = $this->calcularEstatisticasInatividade($clientes_id);

		$dados = $dados->paginate(30);

        $filtros = array(
            'page' => $request->get('page',0),
            'termo' => $termo,
            'clientes_id'=> $clientes_id,
            'unidade_id' => $unidade_id
		);

		$paginacao = $dados->appends($filtros + ['total'=>$total])->links();

        $clientes = Clientes::where('ativo','=',1)->orderBy('nome','ASC')->get();

        $unidades = [];
        if($clientes_id) {
            $unidades = Unidades::where('clientes_id','=',$clientes_id)->orderBy('nome')->get();
        }

        $params = $filtros + array(
            'dados' => $dados,
            'registro' => $registro,
            'filtros' => $filtros,
            'clientes' => $clientes,
            'permissao' => $request->session()->get('logado'),
            'unidades' => $unidades,
            'estatisticas_inatividade' => $estatisticasInatividade
        );

        return view('admin.consumidores')->with($params);
    }



    /**
     * Inserir novo registro ou alterar um existente
     *
     * @return Response
     */
    public function salvar(Request $request)
    {

        $dados = $request->all();

        if($request->get('senha') != '') {
            $dados['senha'] = Hash::make($request->get('senha'));
        }
        else {
            unset($dados['senha']);
        }


        $salvo = Consumidores::salvar($dados);

        if(!$request->has('id')) {
            $salvo->token = Hash::make($salvo->id);
            $salvo->save();
        }

        //gera token
        if($salvo->token == '') {
            $salvo->token = Hash::make($salvo->id);
            $salvo->save();
        }


		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.save.success')));
    }



	/**
	 * Deletar registro
	 *
	 * @param  int  $id
	 */
	 public function apagar(Request $request, $id)
	 {

        try {

            if($request->session()->get('logado') == 'cliente') {
                $retorno = Cadastros::where('consumidores_id','=',$id)
                                ->where('clientes_id','=',$request->session()->get('logado_usuario')->clientes_id)
                                ->delete();
            }

            if($request->session()->get('logado') == 'gestor') {
                Cadastros::where('consumidores_id','=',$id)->delete();
                Acessos::where('consumidores_id','=',$id)->delete();
                $retorno = Consumidores::find($id)->delete();
            }
        }
        catch(\Exception $e) {
			return back()->withErrors(array('error'=>__('messages.delete.error')));
        }

		// verifica se deu erro
        $dados = [];
		if ($retorno instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$retorno->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.delete.success')));

	 }


	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function atualizar(Request $request, $id, $campo)
	 {

	 	$registro = Consumidores::findOrFail($id);
		$registro->$campo = $request->get('valor');

        $retorno = $registro->save();


		// Retorna o status da operacao
		if($retorno == 0) {
			$response = array(
				'status' => 'error',
				'message' => __('messages.update.error'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		} else {
			$response = array(
				'status' => 'success',
				'message' => __('messages.update.success'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		}
	 }





    /**
     * Inserir foto
     *
     * @return Response
     */
    public function imagem(Request $request)
    {

        $dados = $request->all();
        $dados['ativo'] = 0;
        $registro = Consumidores::find($dados['consumidores_id']);

        $upload = $request->file('imagem')->storeAs('img/consumidores/'.date('Ym'), $registro->id.'l'.date('s').'.'.$request->file('imagem')->extension());
        $image = Image::make($upload)->widen(800, function ($constraint) {$constraint->upsize();})->save($upload);

        //apaga antiga
        if($registro->imagem != '') {
            Storage::delete($registro->imagem);
        }

        //salva nova
        $registro->imagem = $upload;
        $salvo = $registro->save();


		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.save.success')));
    }



	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function imagemexcluir(Request $request, $id)
	 {

        $registro = Consumidores::find($id);
        Storage::delete($registro->imagem);
        $registro->imagem = null;
        $registro->save();

        return back()->withErrors(array('success' => __('messages.save.success')));

	 }




    /**
     * Exportar dados
     *
     * @return Response
     */
    public function exportar(Request $request)
    {

        $dados = $request->all();
        $dados['dados'] = array(
            'id',
            'nome',
            'tipopessoa',
            'cadastronacional',
            'email',
            'celular',
            'nascimento',
            'ativo',
            'created_at',
            'updated_at'
        );


        if($request->session()->get('logado') == 'cliente') {
            $dados['clientes_id'] = $request->session()->get('logado_usuario')->clientes_id;
        }

        return Excel::download(new ConsumidoresExport($dados, $request), 'consumidores_'.date('dmy').'.xlsx');

    }



	/**
	 * Notificar registro
	 *
	 * @param  int  $id
	 */
    public function notificar($id, Request $request)
    {
       try {
           $consumidor = Consumidores::find($id);
           $dados_consumidores = $consumidor->toArray();
           $dados_consumidores['clientes_id'] = $request->session()->get('logado_usuario')->clientes_id;
            // NOTIFICAR
            $dados_notificacao = array(
                'tipo' => 'consumidorsaldo',
                'template' => 'consumidorsaldo',
                'variaveis' => serialize($dados_consumidores),
                'email' => $consumidor->email,
                'celular' => $consumidor->celular,
                'nome' => $consumidor->nome,
                'assunto' => 'Você pode ganhar um prêmio',
                'canais' => 'email',
                'prioridade' => 3,
                'status' => 0
            );

            $notificacao = Notificacoes::salvar($dados_notificacao);

       }
       catch(\Exception $e) {
           return back()->withErrors(array('error'=>__('messages.email.error')));
       }


       return back()->with($request->all())->withErrors(array('success' => __('messages.email.success')));

    }

    /**
     * Calcular estatísticas de inatividade de forma otimizada
     */
    private function calcularEstatisticasInatividade($clientes_id = null)
    {
        try {
            // Query base para consumidores
            $query = "
                SELECT
                    COUNT(*) as total,
                    SUM(CASE
                        WHEN dias_inatividade IS NULL THEN 1
                        ELSE 0
                    END) as sem_acesso,
                    SUM(CASE
                        WHEN dias_inatividade <= 30 THEN 1
                        ELSE 0
                    END) as ativo_30_dias,
                    SUM(CASE
                        WHEN dias_inatividade > 30 AND dias_inatividade <= 60 THEN 1
                        ELSE 0
                    END) as ativo_60_dias,
                    SUM(CASE
                        WHEN dias_inatividade > 60 AND dias_inatividade <= 90 THEN 1
                        ELSE 0
                    END) as ativo_90_dias,
                    SUM(CASE
                        WHEN dias_inatividade > 90 THEN 1
                        ELSE 0
                    END) as inativo_90_dias
                FROM (
                    SELECT
                        c.id,
                        CASE
                            WHEN a.ultimo_acesso IS NULL AND p.ultimo_ponto IS NULL THEN NULL
                            WHEN a.ultimo_acesso IS NULL THEN DATEDIFF(NOW(), p.ultimo_ponto)
                            WHEN p.ultimo_ponto IS NULL THEN DATEDIFF(NOW(), a.ultimo_acesso)
                            ELSE LEAST(DATEDIFF(NOW(), a.ultimo_acesso), DATEDIFF(NOW(), p.ultimo_ponto))
                        END as dias_inatividade
                    FROM consumidores c
                    " . ($clientes_id ? "
                    INNER JOIN cadastros cad ON cad.consumidores_id = c.id
                    AND cad.clientes_id = " . intval($clientes_id) : "") . "
                    LEFT JOIN (
                        SELECT consumidores_id, MAX(created_at) as ultimo_acesso
                        FROM acessos
                        GROUP BY consumidores_id
                    ) a ON a.consumidores_id = c.id
                    LEFT JOIN (
                        SELECT consumidores_id, MAX(created_at) as ultimo_ponto
                        FROM pontos
                        WHERE status = 1
                        " . ($clientes_id ? "AND clientes_id = " . intval($clientes_id) : "") . "
                        GROUP BY consumidores_id
                    ) p ON p.consumidores_id = c.id
                ) as stats
            ";

            $resultado = \DB::select($query);

            if (!empty($resultado)) {
                $stats = $resultado[0];
                return [
                    'total' => $stats->total,
                    'sem_acesso' => $stats->sem_acesso,
                    'ativo_30_dias' => $stats->ativo_30_dias,
                    'ativo_60_dias' => $stats->ativo_60_dias,
                    'ativo_90_dias' => $stats->ativo_90_dias,
                    'inativo_90_dias' => $stats->inativo_90_dias
                ];
            }
        } catch(\Exception $e) {
            // Em caso de erro, retornar valores zerados
        }

        return [
            'total' => 0,
            'sem_acesso' => 0,
            'ativo_30_dias' => 0,
            'ativo_60_dias' => 0,
            'ativo_90_dias' => 0,
            'inativo_90_dias' => 0
        ];
    }

}