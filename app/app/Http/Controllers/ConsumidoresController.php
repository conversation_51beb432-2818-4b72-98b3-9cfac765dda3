<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ConsumidoresExport;

use App\Consumidores;
use App\Clientes;
use App\Cadastros;
use App\Acessos;
use App\Notificacoes;
use App\Unidades;

class ConsumidoresController extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id = null)
    {


        $registro = new Consumidores;
        if($id) {
            $registro = Consumidores::findOrFail($id);
        }
        if($request->session()->has('registro')) {
            $registro = new Consumidores($request->session()->get('registro'));
        }

        $unidade_id = $request->get('unidade_id');

        $dados = Consumidores::select('consumidores.*')
                    ->orderBy('created_at', 'DESC');


        $clientes_id = $request->get('clientes_id',null);

        if($request->session()->get('logado') == 'cliente') {
            $clientes_id = $request->session()->get('logado_usuario')->clientes_id;
        }
        if($clientes_id) {
            $dados->join('cadastros','cadastros.consumidores_id','=','consumidores.id');
            $dados->where('cadastros.clientes_id','=',$clientes_id);
        }

        if(!empty($unidade_id)) {
            $dados->where('consumidores.unidade_id','=', $unidade_id);
        }

        $termo = $request->get('termo',null);
        if($termo) {
            $dados->whereRaw('(consumidores.nome LIKE "%'.$termo.'%" OR consumidores.email LIKE "%'.$termo.'%")');
        }

        $total = $dados->count();

        // Calcular estatísticas de inatividade antes da paginação
        $dadosCompletos = clone $dados;
        $todosConsumidores = $dadosCompletos->get();

        $estatisticasInatividade = [
            'total' => $total,
            'ativo_30_dias' => 0,
            'ativo_60_dias' => 0,
            'ativo_90_dias' => 0,
            'inativo_90_dias' => 0,
            'sem_acesso' => 0
        ];

        foreach($todosConsumidores as $consumidor) {
            // Verificar último acesso
            $ultimoAcesso = $consumidor->acessos()->first();
            $ultimoPonto = $consumidor->pontos($clientes_id)->first();

            $diasUltimoAcesso = null;
            $diasUltimoPonto = null;

            if($ultimoAcesso) {
                $diasUltimoAcesso = \Carbon\Carbon::parse(\Carbon\Carbon::now())->diffInDays($ultimoAcesso->created_at);
            }

            if($ultimoPonto) {
                $diasUltimoPonto = \Carbon\Carbon::parse(\Carbon\Carbon::now())->diffInDays($ultimoPonto->created_at);
            }

            // Usar o menor número de dias entre acesso e pontos (mais recente)
            $diasInatividade = null;
            if($diasUltimoAcesso !== null && $diasUltimoPonto !== null) {
                $diasInatividade = min($diasUltimoAcesso, $diasUltimoPonto);
            } elseif($diasUltimoAcesso !== null) {
                $diasInatividade = $diasUltimoAcesso;
            } elseif($diasUltimoPonto !== null) {
                $diasInatividade = $diasUltimoPonto;
            }

            // Categorizar por inatividade
            if($diasInatividade === null) {
                // Se não tem nem acesso nem pontos, conta como sem acesso
                $estatisticasInatividade['sem_acesso']++;
            } elseif($diasInatividade <= 30) {
                $estatisticasInatividade['ativo_30_dias']++;
            } elseif($diasInatividade <= 60) {
                $estatisticasInatividade['ativo_60_dias']++;
            } elseif($diasInatividade <= 90) {
                $estatisticasInatividade['ativo_90_dias']++;
            } else {
                $estatisticasInatividade['inativo_90_dias']++;
            }
        }

		$dados = $dados->paginate(30);

        $filtros = array(
            'page' => $request->get('page',0),
            'termo' => $termo,
            'clientes_id'=> $clientes_id,
            'unidade_id' => $unidade_id
		);

		$paginacao = $dados->appends($filtros + ['total'=>$total])->links();

        $clientes = Clientes::where('ativo','=',1)->orderBy('nome','ASC')->get();

        $unidades = [];
        if($clientes_id) {
            $unidades = Unidades::where('clientes_id','=',$clientes_id)->orderBy('nome')->get();
        }

        $params = $filtros + array(
            'dados' => $dados,
            'registro' => $registro,
            'filtros' => $filtros,
            'clientes' => $clientes,
            'permissao' => $request->session()->get('logado'),
            'unidades' => $unidades,
            'estatisticas_inatividade' => $estatisticasInatividade
        );

        return view('admin.consumidores')->with($params);
    }



    /**
     * Inserir novo registro ou alterar um existente
     *
     * @return Response
     */
    public function salvar(Request $request)
    {

        $dados = $request->all();

        if($request->get('senha') != '') {
            $dados['senha'] = Hash::make($request->get('senha'));
        }
        else {
            unset($dados['senha']);
        }


        $salvo = Consumidores::salvar($dados);

        if(!$request->has('id')) {
            $salvo->token = Hash::make($salvo->id);
            $salvo->save();
        }

        //gera token
        if($salvo->token == '') {
            $salvo->token = Hash::make($salvo->id);
            $salvo->save();
        }


		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.save.success')));
    }



	/**
	 * Deletar registro
	 *
	 * @param  int  $id
	 */
	 public function apagar(Request $request, $id)
	 {

        try {

            if($request->session()->get('logado') == 'cliente') {
                $retorno = Cadastros::where('consumidores_id','=',$id)
                                ->where('clientes_id','=',$request->session()->get('logado_usuario')->clientes_id)
                                ->delete();
            }

            if($request->session()->get('logado') == 'gestor') {
                Cadastros::where('consumidores_id','=',$id)->delete();
                Acessos::where('consumidores_id','=',$id)->delete();
                $retorno = Consumidores::find($id)->delete();
            }
        }
        catch(\Exception $e) {
			return back()->withErrors(array('error'=>__('messages.delete.error')));
        }

		// verifica se deu erro
        $dados = [];
		if ($retorno instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$retorno->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.delete.success')));

	 }


	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function atualizar(Request $request, $id, $campo)
	 {

	 	$registro = Consumidores::findOrFail($id);
		$registro->$campo = $request->get('valor');

        $retorno = $registro->save();


		// Retorna o status da operacao
		if($retorno == 0) {
			$response = array(
				'status' => 'error',
				'message' => __('messages.update.error'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		} else {
			$response = array(
				'status' => 'success',
				'message' => __('messages.update.success'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		}
	 }





    /**
     * Inserir foto
     *
     * @return Response
     */
    public function imagem(Request $request)
    {

        $dados = $request->all();
        $dados['ativo'] = 0;
        $registro = Consumidores::find($dados['consumidores_id']);

        $upload = $request->file('imagem')->storeAs('img/consumidores/'.date('Ym'), $registro->id.'l'.date('s').'.'.$request->file('imagem')->extension());
        $image = Image::make($upload)->widen(800, function ($constraint) {$constraint->upsize();})->save($upload);

        //apaga antiga
        if($registro->imagem != '') {
            Storage::delete($registro->imagem);
        }

        //salva nova
        $registro->imagem = $upload;
        $salvo = $registro->save();


		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.save.success')));
    }



	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function imagemexcluir(Request $request, $id)
	 {

        $registro = Consumidores::find($id);
        Storage::delete($registro->imagem);
        $registro->imagem = null;
        $registro->save();

        return back()->withErrors(array('success' => __('messages.save.success')));

	 }




    /**
     * Exportar dados
     *
     * @return Response
     */
    public function exportar(Request $request)
    {

        $dados = $request->all();
        $dados['dados'] = array(
            'id',
            'nome',
            'tipopessoa',
            'cadastronacional',
            'email',
            'celular',
            'nascimento',
            'ativo',
            'created_at',
            'updated_at'
        );


        if($request->session()->get('logado') == 'cliente') {
            $dados['clientes_id'] = $request->session()->get('logado_usuario')->clientes_id;
        }

        return Excel::download(new ConsumidoresExport($dados, $request), 'consumidores_'.date('dmy').'.xlsx');

    }



	/**
	 * Notificar registro
	 *
	 * @param  int  $id
	 */
    public function notificar($id, Request $request)
    {
       try {
           $consumidor = Consumidores::find($id);
           $dados_consumidores = $consumidor->toArray();
           $dados_consumidores['clientes_id'] = $request->session()->get('logado_usuario')->clientes_id;
            // NOTIFICAR
            $dados_notificacao = array(
                'tipo' => 'consumidorsaldo',
                'template' => 'consumidorsaldo',
                'variaveis' => serialize($dados_consumidores),
                'email' => $consumidor->email,
                'celular' => $consumidor->celular,
                'nome' => $consumidor->nome,
                'assunto' => 'Você pode ganhar um prêmio',
                'canais' => 'email',
                'prioridade' => 3,
                'status' => 0
            );

            $notificacao = Notificacoes::salvar($dados_notificacao);

       }
       catch(\Exception $e) {
           return back()->withErrors(array('error'=>__('messages.email.error')));
       }


       return back()->with($request->all())->withErrors(array('success' => __('messages.email.success')));

    }



}