<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

use Illuminate\Support\Facades\Mail;
use App\Http\Controllers\Notificacao;

use App\Clientes;
use App\Premios;
use App\Acessos;
use App\Categorias;

class ClientesController extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id = null)
    {

        if($request->session()->get('logado') == 'cliente') {
            return back()->withErrors(array('error' => 'Sem permissão'));
        }

        $registro = new Clientes;
        if($id) {
            $registro = Clientes::findOrFail($id);
        }
        if($request->session()->has('registro')) {
            $registro = new Clientes($request->session()->get('registro'));
        }

        $dados = Clientes::select('clientes.*', 'categorias.nome as categoria')
                    ->leftjoin('categorias','categorias.id','=','clientes.categorias_id')
                    ->orderBy('clientes.nome', 'ASC');


        $termo = $request->get('termo',null);
        if($termo) {
            $dados->whereRaw('(clientes.nome LIKE "%'.$termo.'%" OR clientes.email LIKE "%'.$termo.'%")');
        }

        $tag_filtro = $request->get('tag_filtro',null);
        if($tag_filtro) {
            $dados->whereRaw('(clientes.tags LIKE "%'.$tag_filtro.'%")');
        }

        $total = $dados->count();
		$dados = $dados->paginate(30);

        $filtros = array(
            'page' => $request->get('page',0),
            'termo' => $termo,
            'tag_filtro' => $tag_filtro
		);

		$paginacao = $dados->appends($filtros + ['total'=>$total])->links();

        $categorias = Categorias::orderBy('nome','ASC')->get();
        $params = $filtros + array(
            'dados' => $dados,
            'registro' => $registro,
            'filtros' => $filtros,
            'categorias' => $categorias
        );


        return view('admin.clientes')->with($params);
    }



    /**
     * Inserir novo registro ou alterar um existente
     *
     * @return Response
     */
    public function salvar(Request $request)
    {

        $dados = $request->all();

        if($request->get('senha') != '') {
            $dados['senha'] = Hash::make($request->get('senha'));
        }
        else {
            unset($dados['senha']);
        }

        if ($request->get('cor_padrao')){
            $dados['bg_color'] = null;
            $dados['bg_color2'] = null;
        }

        $salvo = Clientes::salvar($dados);

		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.save.success')));
    }



	/**
	 * Deletar registro
	 *
	 * @param  int  $id
	 */
	 public function apagar($id)
	 {

        try {
            $retorno = Clientes::find($id)->delete();
        }
        catch(\Exception $e) {
			return back()->withErrors(array('error'=>__('messages.delete.error')));
        }

		// verifica se deu erro
		if ($retorno instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$retorno->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.delete.success')));

	 }


	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function atualizar(Request $request, $id, $campo)
	 {

	 	$registro = Clientes::findOrFail($id);
		$registro->$campo = $request->get('valor');

        $retorno = $registro->save();


		// Retorna o status da operacao
		if($retorno == 0) {
			$response = array(
				'status' => 'error',
				'message' => __('messages.update.error'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		} else {
			$response = array(
				'status' => 'success',
				'message' => __('messages.update.success'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		}
	 }





    /**
     * Inserir foto
     *
     * @return Response
     */
    public function imagem(Request $request)
    {

        $dados = $request->all();
        $registro = Clientes::find($dados['clientes_id']);

        $upload = $request->file('imagem')->storeAs('img/clientes/'.date('Ym'), $registro->id.'l'.date('s').'.'.$request->file('imagem')->extension());
        $image = Image::make($upload)->widen(800, function ($constraint) {$constraint->upsize();})->save($upload);

        //apaga antiga
        if($registro->imagem != '') {
            Storage::delete($registro->imagem);
        }

        //salva nova
        $registro->imagem = $upload;
        $salvo = $registro->save();


		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.save.success')));
    }



	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function imagemexcluir(Request $request, $id)
	 {

        $registro = Clientes::find($id);
        Storage::delete($registro->imagem);
        $registro->imagem = null;
        $registro->save();

        return back()->withErrors(array('success' => __('messages.save.success')));

	 }




    /**
     * Enviar notificação
     *
     * @return Response
     */
    public function notificar(Request $request, $id)
    {

        $tipo = 'notification';
        // enviar notificacao ao loja
        $loja = Clientes::find($id);
        $text = 'Lembre de adicionar os pontos';
        $title = $loja->nome;
        $targeturl = url('')."/clientes?token=".$loja->token;
        $tag = $loja->token;

        $verifica_se_push = PushController::verificarInstalacao($tag, $request);
        if($verifica_se_push) {
            $envio = PushController::envio($text, $title, $targeturl, $tag, $request);
            if($envio != 'Success') {
                return back()->withErrors(array('error' => $envio));
            }
        } else {
            //envia sms
            $celular = str_replace('(','',$loja->celular);
            $celular = str_replace(')','',$celular);
            $celular = str_replace('-','',$celular);
            $celular = str_replace(' ','',$celular);
            $celular = '55'.$celular;
            PushController::enviasms($celular, $title.': '.$text.' - '.url(''));
            $tipo = 'sms';
        }

        $variaveis = array(
            'loja' => $loja
        );
        Mail::to($loja->email)->send(new Notificacao($variaveis, 'email.lembreclientes', $text));

        return back()->withErrors(array('success' => 'Enviado email e o '.$tipo));
    }

    /**
     * Aplicar tags automaticamente baseado em critérios
     */
    public function aplicarTagsAutomaticas()
    {
        $clientes = Clientes::all();

        foreach($clientes as $cliente) {
            // Limpar tags existentes automáticas
            $cliente->removeTag('Novo');
            $cliente->removeTag('Inativo');

            // Aplicar tag "Novo" para clientes criados nos últimos 30 dias
            if($cliente->created_at >= now()->subDays(30)) {
                $cliente->addTag('Novo');
            }

            // Aplicar tag "Inativo" para clientes sem pontos nos últimos 90 dias
            $ultimoPonto = $cliente->pontos()->first();
            if(!$ultimoPonto || $ultimoPonto->created_at <= now()->subDays(90)) {
                $cliente->addTag('Inativo');
            }

            // Aplicar tag "VIP" para clientes com mais de 10000 pontos
            if($cliente->pontuacao() > 10000) {
                $cliente->addTag('VIP');
            }

            $cliente->save();
        }

        return back()->withErrors(array('success' => 'Tags automáticas aplicadas com sucesso!'));
    }

}
