<?php

namespace App;

class Clientes extends BaseModel
{
	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'clientes';

	/**
	 * Gerenciador para os campos created_at e updated_at
	 *
	 * @var string
	 */
	public $timestamps = true;

	/**
	 * Setando os campos que estarao acessiveis
	 */
	protected $fillable = array(
		'id',
		'categorias_id',
		'nome',
        'cnpj',
        'email',
        'celular',
        'imagem',
        'url',
        'conversao',
        'diasvalidade',
        'reaiscashback',
        'premios_id',
        'endereco',
        'cidade',
        'estado',
        'cep',
        'ativo',
        'bg_color',
        'bg_color2',
        'pontos_cadastro',
        'onesignal_api_key',
        'tags'
	);

	/**
	 * Regras de validacao para o model
	 */
	public static $rules = array(
		'id'           => 'integer',
        'categorias_id'=> 'nullable|integer',
        'nome'         => 'required|max:254',
        'email'        => 'email|nullable',
        'conversao'    => 'numeric|nullable',
        'diasvalidade' => 'numeric|nullable',
        'reaicashback' => 'numeric|nullable',
        'url'          => 'required'
	);

	/**
	 * Regras de filtro para o model
	 */
	public static $filters = array(
        'nome'          => 'trim',
        'email'         => 'trim',
        'url'           => 'trim'
	);



    public function pontos()
    {
        return $this->hasMany('App\Pontos')->orderBy('created_at','DESC');
    }

    public function vendas()
    {
        return $this->hasMany('App\Pontos')->where('status','=',1)->sum('valor');
    }

    public function pontuacao()
    {
        return $this->hasMany('App\Pontos')->where('status','=',1)->sum('pontuacao');
    }

    public function premios()
    {
        return $this->hasMany('App\Premios')->where('status','=',1)->orderBy('pontos','ASC');
    }

    public function premiocashback()
    {
        return $this->hasOne('App\Premios','id','premios_id');
    }

    /**
     * Retorna as tags como array
     */
    public function getTagsArray()
    {
        if (empty($this->tags)) {
            return [];
        }
        return array_map('trim', explode(',', $this->tags));
    }

    /**
     * Verifica se o cliente tem uma tag específica
     */
    public function hasTag($tag)
    {
        $tags = $this->getTagsArray();
        return in_array(strtolower(trim($tag)), array_map('strtolower', $tags));
    }

    /**
     * Adiciona uma tag ao cliente
     */
    public function addTag($tag)
    {
        $tags = $this->getTagsArray();
        $tag = trim($tag);

        if (!in_array(strtolower($tag), array_map('strtolower', $tags))) {
            $tags[] = $tag;
            $this->tags = implode(',', $tags);
        }
    }

    /**
     * Remove uma tag do cliente
     */
    public function removeTag($tag)
    {
        $tags = $this->getTagsArray();
        $tag = strtolower(trim($tag));

        $tags = array_filter($tags, function($t) use ($tag) {
            return strtolower(trim($t)) !== $tag;
        });

        $this->tags = implode(',', $tags);
    }

}
