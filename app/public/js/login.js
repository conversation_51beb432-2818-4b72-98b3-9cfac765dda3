document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility for login form
    const togglePassword = document.querySelector('#toggle-password');
    const passwordInput = document.querySelector('#senha');
    const eyeIcon = document.querySelector('#eye-icon');

    if (togglePassword && passwordInput && eyeIcon) {
        togglePassword.addEventListener('click', function() {
            // Toggle between text and password
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle Font Awesome icon
            eyeIcon.className = type === 'password' ? 'fa fa-eye' : 'fa fa-eye-slash';
        });
    }

    // Toggle password visibility for reset form
    const togglePasswordReset = document.querySelector('#toggle-password-reset');
    const passwordInputReset = document.querySelector('#nova-senha');
    const eyeIconReset = document.querySelector('#eye-icon-reset');

    if (togglePasswordReset && passwordInputReset && eyeIconReset) {
        togglePasswordReset.addEventListener('click', function() {
            // Toggle between text and password
            const type = passwordInputReset.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInputReset.setAttribute('type', type);

            // Toggle Font Awesome icon
            eyeIconReset.className = type === 'password' ? 'fa fa-eye' : 'fa fa-eye-slash';
        });
    }

    // Handle form submission
    const loginForm = document.querySelector('#form-login');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            // Add loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = 'Entrando...';
            }
        });
    }

    // Handle forgot password form submission
    const forgotForm = document.querySelector('#form-lembrarsenha');
    if (forgotForm) {
        forgotForm.addEventListener('submit', function(e) {
            // Add loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = 'Enviando...';
            }
        });
    }

    // Handle reset password form submission
    const resetForm = document.querySelector('#form-novasenha');
    if (resetForm) {
        resetForm.addEventListener('submit', function(e) {
            // Add loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = 'Salvando...';
            }
        });
    }
});
