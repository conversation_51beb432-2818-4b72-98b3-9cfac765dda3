body {
    font-family: 'Ubuntu Condensed', sans-serif !important;
    background-color: #FDFDFD !important;
}

.minhacor {
    color: #435887;
}

.meubg {
    background-color: #435887 !important;
}
.meubg2 {
    background-color: #FFB401 !important;
}

h1 {
    font-weight: 800 !important;
    color: #555 !important;
}

#topo {
    background-color: #F9F9F9;
}



#rodape {
    margin-top: 50px;
    background-color: #FF8C01;
    color: #FFF;
    padding: 10px 0;
}



#menu {
    text-transform: uppercase;
    background-color: #435887;
    margin-bottom:20px;
}

#menu i {
    margin-right: 5px;
}

#menu .nav-link {
    margin: 0px 10px;
    font-weight: bold;
    color: #FFF;
}
#menu .nav-link:hover,#menu .active {
    background-color: #FFF;
    color: #FF8C01;
    font-weight: bold;
}
.navbar-light .navbar-brand {color: #F5F5F5 !important}



.tabela-dados thead {
    background-color: #435887;
    color: #FFF;
}

.tabela-dados .acoes {
    width:100px;
}

.atualizar-item-load {
    position: absolute;
    margin-top:-10px
}

.acoes .btn {margin:2px;}


.bootstrap-select.form-control-sm .dropdown-toggle, .bootstrap-select.form-control-lg .dropdown-toggle {
    background-color: #FFF !important;
    border: 1px solid #ced4da !important;
    color: #495057 !important;
}


select[readonly] {
  background: #eee; /*Simular campo inativo */
  pointer-events: none;
  touch-action: none;
}
