/* Modern Login Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    background-color: #ffb000;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}

.container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    width: 100%;
    max-width: 1200px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Login Section */
.login-section {
    padding: 3rem 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.logo {
    width: 200px;
    margin-bottom: 2rem;
}

h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #333;
    text-align: center;
}

/* Form Styles */
.login-form {
    width: 100%;
    max-width: 400px;
}

.input-field {
    width: 100%;
    margin-bottom: 1.25rem;
}

.input-field label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #333;
    font-weight: 500;
}

.input-field input {
    width: 100%;
    padding: 12px 15px;
    font-size: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

.input-field input:focus {
    border-color: #ffb000;
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 176, 0, 0.2);
}

/* Password Container */
.password-container {
    position: relative;
    width: 100%;
}

.password-container input {
    padding-right: 40px;
}

.toggle-password {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    cursor: pointer;
    background: none;
    border: none;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.toggle-password:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.toggle-password .fa {
    font-size: 16px;
    color: #666;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.toggle-password:hover .fa {
    opacity: 1;
    color: #333;
}

.toggle-password:focus {
    outline: 2px solid #ffb000;
    outline-offset: 2px;
}

/* Remember Me */
.remember-me {
    display: flex;
    align-items: center;
    margin: 1rem 0;
}

.remember-me input[type="checkbox"] {
    margin-right: 8px;
    width: auto;
}

.remember-me label {
    margin: 0;
    font-size: 0.9rem;
    color: #555;
}

/* Buttons */
.btn-login {
    width: 100%;
    padding: 12px;
    background-color: #ffb000;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 1rem;
    transition: background-color 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-login:hover {
    background-color: #e6a000;
}

/* Forgot Password */
.forgot-password,
.back-to-login {
    text-align: center;
    margin-top: 1rem;
}

.forgot-password button,
.back-to-login button {
    background: none;
    border: none;
    color: #666;
    font-size: 0.9rem;
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
}

.forgot-password button:hover,
.back-to-login button:hover {
    color: #333;
}

/* Info Section */
.info-section {
    background-color: #727272;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.illustration {
    max-width: 100%;
    height: auto;
}

.illustration img {
    width: 100%;
    height: auto;
    display: block;
}

/* Alerts */
.alert {
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    width: 100%;
    max-width: 400px;
}

.alert-error {
    background-color: #fde8e8;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.alert-success {
    background-color: #e6fffa;
    color: #2c7a7b;
    border: 1px solid #81e6d9;
}

/* User Email */
.user-email {
    background-color: #f0f0f0;
    padding: 8px 15px;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    color: #555;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
        max-width: 500px;
    }

    .info-section {
        display: none;
    }

    .login-section {
        padding: 2rem 1.5rem;
    }
}

/* Animation for form switching */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.login-form-content,
.forgot-password-content,
.reset-password-content {
    animation: fadeIn 0.3s ease-out forwards;
    width: 100%;
    max-width: 400px;
}
