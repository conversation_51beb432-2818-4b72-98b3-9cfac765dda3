/*
 * Win<PERSON>ash Header & Footer Styles
 * Created: 2023
 */

:root {
  --primary-color: #435887;
  --secondary-color: #FF8C01;
  --accent-color: #FFB401;
  --text-color: #333;
  --light-bg: #f8f9fa;
  --dark-bg: #343a40;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* ===== HEADER STYLES ===== */
header {
  width: 100%;
  position: relative;
}

/* Main header container */
#menu.container-fluid {
  background-color: var(--primary-color);
  padding: 0;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: var(--transition);
}

/* Logo container */
#menu a img {
  transition: var(--transition);
}

#menu a:hover img {
  transform: scale(1.05);
}

/* Welcome user section */
#menu .text-white.mb-3 {
  background-color: rgba(0, 0, 0, 0.2) !important;
  padding: 10px 15px !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  letter-spacing: 0.3px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Logout button */
#menu .btn-sm.btn-dark {
  border-radius: 50px !important;
  padding: 5px 15px !important;
  transition: var(--transition);
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#menu .btn-sm.btn-dark:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Alert container */
#alertaretorno.container {
  margin-top: 15px;
}

#alertaretorno .alert {
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

/* Admin header styles */
.header {
  height: 70px;
  background: linear-gradient(to right, #ffffff, #f8f9fa);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 25px;
  z-index: 999;
}

.header-toggle {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 1.5rem;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.header-toggle:hover {
  background-color: rgba(63, 69, 140, 0.1);
  transform: rotate(180deg);
}

.header-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
  font-size: 1.2rem;
  letter-spacing: 0.5px;
}

.header-user {
  display: flex;
  align-items: center;
  background-color: rgba(63, 69, 140, 0.05);
  padding: 8px 15px;
  border-radius: 50px;
  transition: var(--transition);
}

.header-user:hover {
  background-color: rgba(63, 69, 140, 0.1);
}

.header-user-info {
  margin-right: 15px;
  text-align: right;
}

.header-user-name {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: #435887;
  margin: 0;
  font-size: 0.95rem;
}

.header-user-role {
  font-family: 'Poppins', sans-serif;
  font-size: 0.8rem;
  color: #777;
  margin: 0;
}

/* ===== FOOTER STYLES ===== */
footer.container {
  background-color: var(--primary-color);
  color: #fff !important;
  border-radius: 10px 10px 0 0;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.15);
  padding: 20px !important;
  margin-top: 50px;
  position: relative;
  z-index: 10;
  text-align: center;
}

/* Admin footer */
.footer {
  background: #435887;
  color: white;
  padding: 15px 0;
  margin-top: auto;
  width: 100%;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.15);
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
}

.footer strong {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.footer a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: var(--transition);
  padding: 0 5px;
}

.footer a:hover {
  color: white;
  text-decoration: none;
}

.footer .fa-heart {
  color: #ff5252;
  animation: heartbeat 1.5s infinite;
}

/* Footer responsiveness */
@media (max-width: 991px) {
  .footer {
    padding: 20px 0;
  }

  .footer .col-md-6 {
    order: -1;
  }

  .footer .col-md-3 {
    margin: 10px 0;
  }

  .footer .text-md-left,
  .footer .text-md-right {
    text-align: center !important;
  }
}

/* Sticky footer for short content */
html {
  height: 100%;
}

body {
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

main, .content-wrapper {
  flex: 1 0 auto;
}

/* Animation */
@keyframes heartbeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header {
    padding: 0 15px;
  }

  .header-user-info {
    display: none;
  }

  footer.container {
    border-radius: 0;
  }
}
