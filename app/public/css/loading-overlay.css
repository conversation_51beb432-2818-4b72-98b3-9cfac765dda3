/*
 * WinCash Loading Overlay
 * Created: 2023
 */

:root {
  --primary-color: #435887;
  --secondary-color: #4DB287;
  --accent-color: #9DD872;
}

/* Fullscreen Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 25px;
  border-radius: 12px;
  background-color: white;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  max-width: 90%;
  width: 200px;
}

/* Loading Spinner */
.loading-spinner {
  position: relative;
  width: 50px;
  height: 50px;
  margin-bottom: 15px;
}

.loading-spinner:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: var(--primary-color);
  border-bottom-color: var(--secondary-color);
  animation: spin 1s infinite ease-in-out;
}

/* Loading Text */
.loading-text {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: center;
  margin: 0;
  letter-spacing: 0.5px;
  animation: pulse 1.5s infinite ease-in-out;
}

/* Progress Bar - Removed from HTML but keeping styles for future reference */
/*
.loading-progress {
  width: 150px;
  height: 3px;
  background-color: #f0f0f0;
  border-radius: 10px;
  margin-top: 15px;
  overflow: hidden;
  position: relative;
}

.loading-progress-bar {
  height: 100%;
  width: 0;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border-radius: 10px;
  position: absolute;
  top: 0;
  left: 0;
  animation: progress 1.5s ease-in-out infinite;
}
*/

/* Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Progress animation - Removed but keeping for reference
@keyframes progress {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}
*/

/* Pulse Animation for Text */
@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

/* Animation applied in the loading-text definition above */

/* Responsive Adjustments */
@media (max-width: 768px) {
  .loading-container {
    padding: 20px;
    width: 180px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    margin-bottom: 12px;
  }

  .loading-text {
    font-size: 14px;
  }
}
