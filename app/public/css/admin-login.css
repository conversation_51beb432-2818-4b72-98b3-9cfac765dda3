/*
 * WinCash Admin Login Page
 * Created: 2023
 */

:root {
  --primary-color: #435887;
  --secondary-color: #FF8C01;
  --accent-color: #FFB401;
  --text-color: #333;
  --light-bg: #f8f9fa;
  --dark-bg: #343a40;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Login Page Container */
.admin-login-container {
  min-height: 600px;
  display: flex;
  flex-direction: row;
  background-color: #fff;
  overflow: hidden;
  max-width: 1000px;
  margin: 0 auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  overflow: hidden;
}

/* Left Column - Login Form */
.admin-login-form-column {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 1;
  height: 100%;
  min-height: 600px;
}

/* Right Column - Hero Section */
.admin-login-hero-column {
  flex: 1;
  background-color: var(--primary-color);
  background-image: linear-gradient(135deg, var(--primary-color), #2d3266);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding-left: 30px;
  height: 100%;
  min-height: 600px;
}

/* Hero Background Image */
.admin-hero-bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  opacity: 0.2;
  z-index: 0;
}

/* Hero Content */
.admin-hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 90%;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.admin-hero-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
}

.admin-hero-title span {
  color: var(--secondary-color);
  position: relative;
  display: inline-block;
}

.admin-hero-title span::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--secondary-color);
  border-radius: 2px;
}

.admin-hero-subtitle {
  font-size: 0.95rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  max-width: 95%;
  margin: 0 auto;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

/* Login Form Container */
.admin-login-form-wrapper {
  width: 100%;
  max-width: 90%;
  padding: 30px;
  background-color: white;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

/* Logo Container */
.admin-login-logo {
  text-align: center;
  margin-bottom: 30px;
}

.admin-login-logo img {
  max-height: 80px;
  max-width: 100%;
}

/* Form Title */
.admin-login-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 25px;
}

/* Form Fields */
.admin-login-form .form-group {
  margin-bottom: 20px;
}

.admin-login-form .form-control {
  height: 50px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 10px 15px;
  font-size: 16px;
  transition: var(--transition);
}

.admin-login-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(63, 69, 140, 0.25);
}

/* Login Button */
.admin-login-btn {
  border-radius: 8px !important;
  height: 50px;
  font-size: 16px !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px;
  transition: var(--transition);
  width: 100%;
  margin-top: 10px;
  background-color: var(--primary-color) !important;
  color: white !important;
}

.admin-login-btn:hover {
  background-color: #333399 !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Forgot Password Link */
.admin-forgot-password {
  text-align: right;
  margin-bottom: 20px;
}

.admin-forgot-password .btn-link {
  color: var(--primary-color);
  font-size: 14px;
  text-decoration: none;
  transition: var(--transition);
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
}

.admin-forgot-password .btn-link:hover {
  text-decoration: underline;
  color: #333399;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .admin-login-container {
    flex-direction: column-reverse;
    max-width: 100%;
    border-radius: 0;
    box-shadow: none;
    margin: 0;
    height: auto;
    min-height: auto;
  }

  .admin-login-hero-column {
    min-height: 250px;
    height: auto;
    border-radius: 0;
    padding: 40px 20px;
  }

  .admin-login-form-column {
    padding: 40px 20px;
    height: auto;
    min-height: auto;
  }

  .admin-login-form-wrapper {
    max-width: 450px;
    height: auto;
    padding: 20px 0;
  }

  .admin-hero-content {
    height: auto;
  }

  .admin-hero-title {
    font-size: 1.6rem;
  }

  .admin-hero-subtitle {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .admin-login-form-wrapper {
    padding: 20px;
    max-width: 100%;
  }

  .admin-login-hero-column {
    min-height: 200px;
    padding: 30px 20px;
  }

  .admin-hero-title {
    font-size: 1.5rem;
    margin-bottom: 10px;
  }

  .admin-hero-content {
    max-width: 100%;
    padding: 10px 0;
  }

  .hero-decorative-element {
    display: none;
  }
}

/* Form Tabs */
.admin-login-tabs {
  display: flex;
  margin-bottom: 20px;
}

.admin-login-tab {
  flex: 1;
  text-align: center;
  padding: 10px;
  cursor: pointer;
  border-bottom: 2px solid #e0e0e0;
  transition: var(--transition);
  font-weight: 500;
  color: #777;
}

.admin-login-tab.active {
  border-bottom: 2px solid var(--primary-color);
  color: var(--primary-color);
}

/* Form Content */
.admin-login-form-content {
  display: none;
}

.admin-login-form-content.active {
  display: block;
}

/* Remember Me Checkbox */
.admin-remember-me {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.admin-remember-me input[type="checkbox"] {
  margin-right: 10px;
}

.admin-remember-me label {
  margin-bottom: 0;
  font-size: 14px;
  color: #555;
}

/* Alert Messages */
.admin-login-alert {
  margin-bottom: 20px;
  border-radius: 8px;
  padding: 15px;
  font-size: 14px;
}

.admin-login-alert-success {
  background-color: rgba(77, 178, 135, 0.1);
  color: var(--secondary-color);
  border: 1px solid rgba(77, 178, 135, 0.2);
}

.admin-login-alert-error {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}
