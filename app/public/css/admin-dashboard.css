/*
 * WinCash Admin Dashboard Styles
 * Created: 2023
 */

:root {
  --primary-color: #435887;
  --secondary-color: #FF8C01;
  --accent-color: #FFB401;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --success-color: #28a745;
  --text-color: #333;
  --text-light: #f8f9fa;
  --light-bg: #F7F7F8;
  --dark-bg: #1f2937;
  --border-radius: 12px;
  --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dashboard Container */
.dashboard-container {
  padding: 1.5rem;
  max-width: 100%;
}

/* Dashboard Row */
.dashboard-row {
  margin-bottom: 1.5rem;
  display: flex;
  flex-wrap: wrap;
}

/* Dashboard Cards */
.dashboard-card {
  border-radius: 12px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 1.5rem;
  overflow: hidden;
  height: 100%;
  transition: var(--transition);
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.dashboard-card:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-5px);
}

.dashboard-card-header {
  padding: 1.25rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.dashboard-card-header h4 {
  margin: 0;
  font-weight: 600;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
}

.dashboard-card-header h4 i {
  margin-right: 0.75rem;
  font-size: 1.5rem;
}

.dashboard-card-body {
  padding: 1.25rem;
}

.dashboard-card-footer {
  padding: 1rem 1.25rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background-color: rgba(0, 0, 0, 0.03);
}

/* Card Colors */
.card-primary {
  background: #435887;
  color: var(--text-light);
}

.card-secondary {
  background: linear-gradient(135deg, var(--secondary-color), #560bad);
  color: var(--text-light);
}

.card-accent {
  background: linear-gradient(135deg, var(--accent-color), #b5179e);
  color: var(--text-light);
}

.card-danger {
  background: linear-gradient(135deg, var(--danger-color), #ef233c);
  color: var(--text-light);
}

.card-warning {
  background: linear-gradient(135deg, var(--warning-color), #ffb703);
  color: var(--dark-bg);
}

.card-info {
  background: linear-gradient(135deg, var(--info-color), #00b4d8);
  color: var(--text-light);
}

.card-success {
  background: linear-gradient(135deg, var(--success-color), #2dc653);
  color: var(--text-light);
}

.card-light {
  background: linear-gradient(135deg, var(--light-bg), #e9ecef);
  color: var(--text-color);
}

.card-dark {
  background: linear-gradient(135deg, var(--dark-bg), #111827);
  color: var(--text-light);
}

/* Override the .card-light style for Programa Fidelidade card */
.card-light.programa-fidelidade-card {
  background: white !important;
  background-image: none !important;
  color: var(--text-color) !important;
}

/* Override card body for programa fidelidade */
.programa-fidelidade-card .dashboard-card-body {
  background: white !important;
}

/* Ensure the chart has white background */
.programa-fidelidade-card .chart-container {
  background: white !important;
}

/* Stat Cards */
.stat-card {
  text-align: center;
  position: relative;
  overflow: hidden;
  z-index: 1;
  background-color: white;
  color: var(--text-color);
  padding: 1.5rem;
}

/* Dashboard Stats (Pontos, Cadastros, Resgates, Valor, Total) - Special styling for the bottom stats cards */
.card-pontos .stat-card,
.card-cadastros .stat-card,
.card-resgates .stat-card,
.card-valor .stat-card,
.card-total .stat-card {
  padding: 2rem;
  min-height: 0;
}

.card-pontos,
.card-cadastros,
.card-resgates,
.card-valor,
.card-total {
  background: white !important;
  color: var(--text-color) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  background-image: none !important;
  min-height: 0;
}

/* Override any existing gradient or color classes when combined with these cards */
.card-primary.card-pontos,
.card-secondary.card-cadastros,
.card-success.card-resgates,
.card-info.card-valor,
.card-warning.card-total {
  background: white !important;
  background-image: none !important;
  color: var(--text-color) !important;
}

/* Update icon styling for all stat cards */
.stat-card-icon {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
}

.stat-card-icon i {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* Card-specific color styling */
/* Pontos - Blue */
.card-pontos .stat-card-icon i {
  background-color: rgba(67, 88, 135, 0.15);
  color: #435887;
  border-radius: 50%;
  font-size: 1.2rem;
  width: 60px;
  height: 60px;
}
.card-pontos .stat-card-value,
.card-pontos .currency {
  color: #435887 !important;
}
.card-pontos .stat-card-label {
  background-color: rgba(67, 88, 135, 0.1);
  color: #435887;
}

/* Cadastros - Green */
.card-cadastros .stat-card-icon i {
  background-color: rgba(77, 178, 135, 0.15);
  color: #FF8C01;
  border-radius: 50%;
  font-size: 1.2rem;
  width: 60px;
  height: 60px;
}
.card-cadastros .stat-card-value,
.card-cadastros .currency {
  color: #FF8C01 !important;
}
.card-cadastros .stat-card-label {
  background-color: rgba(77, 178, 135, 0.1);
  color: #FF8C01;
}

/* Resgates - Purple */
.card-resgates .stat-card-icon i {
  background-color: rgba(111, 66, 193, 0.15);
  color: #6F42C1;
  border-radius: 50%;
  font-size: 1.2rem;
  width: 60px;
  height: 60px;
}
.card-resgates .stat-card-value,
.card-resgates .currency {
  color: #6F42C1 !important;
}
.card-resgates .stat-card-label {
  background-color: rgba(111, 66, 193, 0.1);
  color: #6F42C1;
}

/* Valor - Orange */
.card-valor .stat-card-icon i {
  background-color: rgba(253, 126, 20, 0.15);
  color: #FD7E14;
  border-radius: 50%;
  font-size: 1.2rem;
  width: 60px;
  height: 60px;
}
.card-valor .stat-card-value,
.card-valor .currency {
  color: #FD7E14 !important;
}
.card-valor .stat-card-label {
  background-color: rgba(253, 126, 20, 0.1);
  color: #FD7E14;
}

/* Total - Teal */
.card-total .stat-card-icon i {
  background-color: rgba(32, 201, 151, 0.15);
  color: #20C997;
  border-radius: 50%;
  font-size: 1.2rem;
  width: 60px;
  height: 60px;
}
.card-total .stat-card-value,
.card-total .currency {
  color: #20C997 !important;
}
.card-total .stat-card-label {
  background-color: rgba(32, 201, 151, 0.1);
  color: #20C997;
}

@keyframes shine {
  0% {
    top: -100%;
    left: -100%;
  }
  20% {
    top: 100%;
    left: 100%;
  }
  100% {
    top: 100%;
    left: 100%;
  }
}

.stat-card h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.9;
}

.stat-card-value {
  font-size: 2.2rem;
  font-weight: 700;
  line-height: 1.2;
  margin: 0.75rem 0;
}

.stat-card-value .currency {
  font-size: 1.2rem;
  font-weight: 500;
  vertical-align: top;
  margin-right: 0.25rem;
}

.stat-card-label {
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.7;
  background-color: rgba(255, 255, 255, 0.1);
  display: inline-block;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  margin-top: 0.5rem;
}

.stat-card-comparison {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 1.2;
}

.stat-card-comparison span {
  margin: 0 0.15rem;
}

/* Chart Cards */
.chart-card {
  padding: 1.5rem;
}

.chart-container {
  height: 300px;
  width: 100%;
  position: relative;
}

/* Filter Buttons */
.filter-buttons {
  display: flex;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.filter-button {
  padding: 0.5rem 1rem;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 50px;
  font-size: 0.85rem;
  font-weight: 500;
  transition: var(--transition);
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: inherit;
}

.filter-button:hover,
.filter-button.active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.filter-button i {
  margin-right: 0.5rem;
}

/* Tables */
.dashboard-table {
  width: 100%;
  margin-bottom: 1rem;
}

.dashboard-table th,
.dashboard-table td {
  padding: 0.75rem;
  vertical-align: middle;
}

.dashboard-table thead th {
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

.dashboard-table tbody tr {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
}

.dashboard-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Scrollable Table Container */
.table-scrollable {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 1rem;
}

/* Progress Bar Styling */
progress {
  -webkit-appearance: none;
  appearance: none;
  border: none;
  border-radius: var(--border-radius);
  overflow: hidden;
}

progress::-webkit-progress-bar {
  background-color: #f0f0f0;
  border-radius: var(--border-radius);
}

progress::-webkit-progress-value {
  background-color: var(--success-color);
  background-image: linear-gradient(45deg,
                    rgba(255, 255, 255, .15) 25%,
                    transparent 25%,
                    transparent 50%,
                    rgba(255, 255, 255, .15) 50%,
                    rgba(255, 255, 255, .15) 75%,
                    transparent 75%,
                    transparent);
  background-size: 1rem 1rem;
  animation: progress-bar-stripes 1s linear infinite;
}

progress::-moz-progress-bar {
  background-color: var(--success-color);
  background-image: linear-gradient(45deg,
                    rgba(255, 255, 255, .15) 25%,
                    transparent 25%,
                    transparent 50%,
                    rgba(255, 255, 255, .15) 50%,
                    rgba(255, 255, 255, .15) 75%,
                    transparent 75%,
                    transparent);
  background-size: 1rem 1rem;
  animation: progress-bar-stripes 1s linear infinite;
  border-radius: var(--border-radius);
}

@keyframes progress-bar-stripes {
  from { background-position: 1rem 0; }
  to { background-position: 0 0; }
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .stat-card-value {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }

  .stat-card-value {
    font-size: 2rem;
  }

  .chart-container {
    height: 250px;
  }
}

@media (max-width: 576px) {
  .filter-buttons {
    flex-direction: column;
  }

  .filter-button {
    width: 100%;
    margin-right: 0;
  }
}

/* Programa Fidelidade Card */
.programa-fidelidade-card {
  background: white !important;
  color: var(--text-color) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  background-image: none !important;
  border-left: 4px solid var(--secondary-color);
}

.programa-fidelidade-card .dashboard-card-header {
  background: white;
  color: var(--text-color);
}

.programa-fidelidade-card .chart-action-btn {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-color);
}

.programa-fidelidade-card .chart-action-btn:hover {
  background-color: var(--primary-color);
  color: white;
}
