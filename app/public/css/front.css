body {
    font-family: 'Outfit', sans-serif !important;
    background-color: #435887 !important;
    color: #4B4B4B !important;
    background-repeat: no-repeat;
    background-position: bottom left;
    zzzbackground-image: url('../img/logo123.png');
}

.minhacor {
    color: #435887;
}
.meubgcor {
    background-color: #435887 !important;
    color: #FFF !important;
    border-color: #435887 !important;
}
.meubgcor2 {
    background-color: #4DB287 !important;
    color: #FFF !important;
    border-color: #4DB287 !important;
}

.meubotao {
    border-radius: 100px !important;
    background: #4DB287 !important;
    box-shadow: 0px -9px 69px -12px rgba(0, 0, 0, 0.25) !important;
    color: #FFF !important;
    font-size: 25px !important;
    line-height:35px !important;
    width: 100% !important;
}
.meubotao:hover {
    background: #9DD872 !important;
    color: #FFF !important;
}

.unidadeitem {
    border-radius: 20px !important;
    line-height:50px !important;
    text-align: left !important;
    height: auto !important;
    padding-left: 30px !important;
    background-color: transparent !important;
    color: #4B4B4B !important;
    box-shadow: none !important;
    font-size: 20px !important;
}

.meubotao2 {
    border-radius: 100px !important;
    background: #FFF !important;
    color: #435887 !important;
    border: 1px solid #4DB287 !important;
    font-size: 25px !important;
    line-height:35px !important;
    width: 100% !important;
    box-shadow: none !important;
}
.meubotaoalerta {
    border-color: #F15E5E !important;
    color: #F15E5E !important
}
.meubotao2:hover {
    background: #435887 !important;
    color: #FFF !important;
    border: 1px solid #FFF !important;
}

.textocor {
    color: #4DB287
}

.bglinha {
    background-image:url('../img/linha.png');
    background-position: bottom left;
    background-repeat:no-repeat;
    padding-bottom:10px;
}


.texto a:link, .texto a:visited {
    color: #4DB287;
    text-decoration: underline;
}
.texto a:hover {
    color: #4DB287 !important;
    text-decoration: none !important;
}



h2 {
    color: #4B4B4B;
    text-align: center;
    font-size: 35px;
    font-style: normal;
    font-weight: 700;
    line-height: 104%;
}

#login, #novasenha, #novasenhaform, #cadastrar, #escolhaunidade {
    border-radius: 20px !important;
}
#form-login, #formcadastro {padding: 0 15%}
.form-control {border-radius: 10px !important}

th, td {padding: 5px 0 0 10px}
