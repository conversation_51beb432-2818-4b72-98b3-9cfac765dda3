#!/bin/bash

# Script para gerar certificados SSL auto-assinados
echo "Gerando certificados SSL para HTTPS..."

# Criar diretório SSL se não existir
mkdir -p docker/nginx/ssl

# Gerar certificados SSL auto-assinados
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout docker/nginx/ssl/wincash.key \
    -out docker/nginx/ssl/wincash.crt \
    -subj "/C=BR/ST=SP/L=SaoPaulo/O=WinCash/OU=Development/CN=localhost"

echo "Certificados SSL gerados com sucesso!"
echo "Agora você pode executar: docker-compose up -d"
echo "Acesse: https://localhost:8443"
