<?php

namespace App;

class Notificacoes extends BaseModel
{
	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'notificacoes';

	/**
	 * Gerenciador para os campos created_at e updated_at
	 *
	 * @var string
	 */
	public $timestamps = true;

	/**
	 * Setando os campos que estarao acessiveis
	 */
	protected $fillable = array(
		'id',
		'tipo',
		'template',
		'variaveis',
		'email',
		'celular',
		'nome',
		'assunto',
		'canais',
		'prioridade',
		'status'
	);

	/**
	 * Regras de validacao para o model
	 */
	public static $rules = array(
		'id'        => 'integer',
        'tipo'      => 'max:254',
        'assunto'   => 'max:254'
	);

	/**
	 * Regras de filtro para o model
	 */
	public static $filters = array(
	);




}
