<?php

namespace App;

class Gestores extends BaseModel
{
	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'gestores';

	/**
	 * Gerenciador para os campos created_at e updated_at
	 *
	 * @var string
	 */
	public $timestamps = false;

	/**
	 * Setando os campos que estarao acessiveis
	 */
	protected $fillable = array(
		'id',
		'clientes_id',
		'nome',
        'email',
        'senha',
        'ativo'
	);

	/**
	 * Regras de validacao para o model
	 */
	public static $rules = array(
		'id'            => 'integer',
        'clientes_id'   => 'integer|nullable',
        'nome'          => 'required|max:254',
        'email'         => 'email|nullable'
	);

	/**
	 * Regras de filtro para o model
	 */
	public static $filters = array(
        'nome'          => 'trim'
	);




}
