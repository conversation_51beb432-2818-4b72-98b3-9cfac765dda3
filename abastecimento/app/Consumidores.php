<?php

namespace App;

class Consumidores extends BaseModel
{
	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'consumidores';

	/**
	 * Gerenciador para os campos created_at e updated_at
	 *
	 * @var string
	 */
	public $timestamps = true;

	/**
	 * Setando os campos que estarao acessiveis
	 */
	protected $fillable = array(
		'id',
		'clientes_id',
		'codigo',
		'nome',
		'tipopessoa',
		'cadastronacional',
        'email',
		'senha',
        'celular',
        'nascimento',
		'token',
		'diapagamento',
		'valorlimitepagamento',
        'ativo'
	);

	/**
	 * Regras de validacao para o model
	 */
	public static $rules = array(
		'id'            => 'integer',
        'clientes_id'   => 'integer|required',
        'nome'          => 'required|max:254',
        'email'         => 'email|nullable',
        'nascimento'    => 'date|nullable'
	);

	/**
	 * Regras de filtro para o model
	 */
	public static $filters = array(
        'nome'          => 'trim'
	);

    protected $dates = [
        'nascimento'
    ];

    public function cliente()
    {
        return $this->hasOne('App\Clientes','id','clientes_id');
    }

    public function creditos()
    {
        return $this->hasMany('App\Creditos','id','consumidores_id');
    }

    public function calculaSaldo()
    {

        $soma = $this->hasMany('App\Creditos')
                        ->where('status','=',1)
                        ->sum('valor');

        $this->saldo = $soma;
        $this->save();

        return $soma;
    }


}
