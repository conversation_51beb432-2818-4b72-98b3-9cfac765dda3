<?php

namespace App;

class Vendedores extends BaseModel
{
	/**
	 * The database table used by the model.
	 *
	 * @var string
	 */
	protected $table = 'vendedores';

	/**
	 * Gerenciador para os campos created_at e updated_at
	 *
	 * @var string
	 */
	public $timestamps = true;

	/**
	 * Setando os campos que estarao acessiveis
	 */
	protected $fillable = array(
		'id',
		'clientes_id',
		'nome',
        'email',
        'celular',
        'nascimento',
        'status'
	);

	/**
	 * Regras de validacao para o model
	 */
	public static $rules = array(
		'id'            => 'integer',
        'clientes_id'   => 'integer|required',
        'nome'          => 'required|max:254',
        'email'         => 'email|nullable',
        'nascimento'    => 'date|nullable'
	);

	/**
	 * Regras de filtro para o model
	 */
	public static $filters = array(
        'nome'          => 'trim'
	);

    protected $dates = [
        'nascimento'
    ];

    public function cliente()
    {
        return $this->hasOne('App\Clientes','id','clientes_id');
    }


}
