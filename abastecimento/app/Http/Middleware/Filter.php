<?php

namespace App\Http\Middleware;

class Filter
{
	/**
	 * Metodo de filtro hidratando os fields
	 *
	 * @param  array  $data
	 * @param  array  $filters
	 * @return \Filters
	 */
	public static function make(array $data = array(), array $filters = array())
	{
		// percorre os campos 
		foreach ($filters as $key => $filter) {
			// verifica se existe filtro e valor para ser filtrado			
			if (!empty($filter)) {
				// efetua o filtro
				$data[$key] = isset($data[$key]) ? Filter::$filter($data[$key]) : Filter::$filter(null);
			} else {
				// pula para o proximo registro
				continue;
			}
		}

		return $data;
	}

	/**
	 * Retira os espacos vazio de antes e depois da palavra/frase
	 *
	 * @var string
	 * @return string
	 */
	public static function trim($string = null)
	{
		return trim($string);
	}

	/**
	 * Retorna somente os digitos de uma string
	 *
	 * @var string
	 * @return string
	 */
	public static function digits($value = null)
	{
	    if(empty($value)) return null;
		return preg_replace('/[^[:digit:]]/', '', (string) $value);
	}

	/**
	 * Formata numeros decimais
	 *
	 * @var decimal $value
	 * @return number_format
	 */
	public static function amount($value)
	{
	    if(empty($value)) return 0;
    	return str_replace(',', '.', $value);
	}

	/**
	 * Formata numeros booleans
	 *
	 * @var $value
	 * @return boolean
	 */
	public static function boolean($value)
	{
		return (boolean) $value;
	}

	/**
	 * Inverte uma data independente do formato que for passado
	 *
	 * @var date
	 * @return format date
	 */
	public static function dateformat($date = null)
	{
		// verifica se foi informada a data
		if (!empty($date)) {
			// inverte
			$date = implode('-', array_reverse(explode("/", $date)));
		}

		return $date;
	}

	/**
	 * Retira caracteres
	 *
	 * @var date
	 * @return format date
	 */
	public static function telefone($value)
	{
		// verifica se foi informada a data
		$value = str_replace('(','',$value);
		$value = str_replace(')','',$value);
		$value = str_replace('-','',$value);
		$value = str_replace(' ','',$value);

		return $value;
	}

}