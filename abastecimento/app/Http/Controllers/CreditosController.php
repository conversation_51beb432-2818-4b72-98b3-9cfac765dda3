<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Http\Middleware\Filter;
use Illuminate\Support\Facades\Hash;

use App\Creditos;
use App\Consumidores;
use App\Vendedores;
use App\Veiculos;
use App\Documentos;
use App\Pagamentos;
use App\Motoristas;


class CreditosController extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id = null)
    {


        $registro = new Creditos;
        $pagamentos = new Pagamentos;
        $documentos = new Documentos;
        $consumidorFiltro = new Consumidores;
        if($id) {
            $registro = Creditos::find($id);
            if(empty($registro)) {
                return redirect()->route('creditos');
            }
            $pagamentos = Pagamentos::where('creditos_id','=',$id)->orderBy('dtpagamento','ASC')->get();
            $documentos = Documentos::where('creditos_id','=',$id)->orderBy('created_at','DESC')->get();
            if($request->has('cards')) {
                $request->session()->flash('cardpagamentos', true);
                $request->session()->flash('carddocumentos', true);
            }

        }
        if($request->session()->has('registro')) {
            $registro = new Creditos($request->session()->get('registro'));
        }

        $dados = Creditos::select('creditos.*', 'consumidores.nome AS consumidor', 'veiculos.descricao AS veiculo', 'veiculos.placa', 'vendedores.nome AS vendedor', 'clientes.nome AS cliente', 'veiculos.tipo AS veiculotipo', 'motoristas.nome AS motorista')
                    ->join('consumidores','consumidores.id','=','creditos.consumidores_id')
                    ->join('clientes','clientes.id','=','consumidores.clientes_id')
                    ->leftjoin('vendedores','vendedores.id','=','creditos.vendedores_id')
                    ->leftjoin('veiculos','veiculos.id','=','creditos.veiculos_id')
                    ->leftjoin('motoristas','motoristas.id','=','creditos.motoristas_id')
                    ->orderBy('dtcompra', 'DESC')
                    ->orderBy('created_at', 'DESC');


        $clientes_id = $request->session()->get('logado_usuario')->clientes_id;
        if($clientes_id) {
            $dados->where('consumidores.clientes_id','=',$clientes_id);
        }

        $consumidores_id = $request->get('consumidores_id',null);
        if($request->session()->get('logado') == 'consumidor') {
            $consumidores_id = $request->session()->get('logado_usuario')->id;
        }
        if($consumidores_id) {
            $dados->where('creditos.consumidores_id','=',$consumidores_id);
            $consumidorFiltro = Consumidores::find($consumidores_id);
        }

        $tipovalor = $request->get('tipovalor',null);
        if($tipovalor) {
            if($tipovalor == 'abastecimento') {
                $dados->where('creditos.valor','>',0);
            }
            if($tipovalor == 'fatura') {
                $dados->where('creditos.valor','<',0);
            }
        }

        $statusvalor = $request->get('statusvalor',null);
        if($statusvalor !== null) {
                $dados->where('creditos.status','=',$statusvalor);
        }

        $dtde = $request->get('dtde',null);
        if($dtde) {
            $dados->where('creditos.dtcompra','>=',$dtde);
        }

        $dtate = $request->get('dtate',null);
        if($dtate) {
            $dados->where('creditos.dtcompra','<=',$dtate);
        }

        $termo = $request->get('termo',null);
        if($termo) {
            $dados->whereRaw('(clientes.nome LIKE "%'.$termo.'%" OR consumidores.nome LIKE "%'.$termo.'%" OR veiculos.descricao LIKE "%'.$termo.'%" OR creditos.comentarios LIKE "%'.$termo.'%")');
        }

        $total = $dados->count();
		$dados = $dados->paginate(50);

        $filtros = array(
            'page' => $request->get('page',0),
            'termo' => $termo,
            'consumidores_id' => $consumidores_id,
            'tipovalor' => $tipovalor,
            'statusvalor' => $statusvalor,
            'dtde' => $dtde,
            'dtate' => $dtate
		);

		$paginacao = $dados->appends($filtros + ['total'=>$total])->links();

        $consumidores = Consumidores::where('status','=',1)->orderBy('nome','ASC')->get();

        $clientes_id = $request->session()->get('logado_usuario')->clientes_id;
        $consumidores = Consumidores::select('consumidores.*', 'clientes.nome AS cliente')->join('clientes','clientes.id','=','consumidores.clientes_id')->where('consumidores.status','=',1)->orderBy('consumidores.nome','ASC');
        $veiculos = Veiculos::select('veiculos.*', 'consumidores.nome AS consumidor', 'clientes.nome AS cliente')
                            ->join('consumidores','consumidores.id','=','veiculos.consumidores_id')
                            ->join('clientes','clientes.id','=','consumidores.clientes_id')
                            ->where('clientes.ativo','=',1)
                            ->where('consumidores.status','=',1)
                            ->where('veiculos.status','=',1)
                            ->orderBy('clientes.nome','ASC')
                            ->orderBy('consumidores.nome','ASC')
                            ->orderBy('veiculos.descricao','ASC');

        $motoristas = Motoristas::select('motoristas.*', 'consumidores.nome AS consumidor', 'clientes.nome AS cliente')
                            ->join('consumidores','consumidores.id','=','motoristas.consumidores_id')
                            ->join('clientes','clientes.id','=','consumidores.clientes_id')
                            ->where('clientes.ativo','=',1)
                            ->where('consumidores.status','=',1)
                            ->where('motoristas.status','=',1)
                            ->orderBy('clientes.nome','ASC')
                            ->orderBy('consumidores.nome','ASC')
                            ->orderBy('motoristas.nome','ASC');


        $vendedores = Vendedores::select('vendedores.*', 'clientes.nome AS cliente')
                            ->join('clientes','clientes.id','=','vendedores.clientes_id')
                            ->where('clientes.ativo','=',1)
                            ->where('vendedores.status','=',1)
                            ->orderBy('clientes.nome','ASC')
                            ->orderBy('vendedores.nome','ASC');


        if($clientes_id) {
            $consumidores = $consumidores->where('clientes_id','=',$clientes_id);
            $veiculos = $veiculos->where('clientes_id','=',$clientes_id);
            $vendedores = $vendedores->where('clientes_id','=',$clientes_id);
            $motoristas = $motoristas->where('clientes_id','=',$clientes_id);
        }
        $consumidores = $consumidores->get();
        $veiculos = $veiculos->get();
        $motoristas = $motoristas->get();
        $vendedores = $vendedores->get();

        $params = $filtros + array(
            'dados' => $dados,
            'registro' => $registro,
            'filtros' => $filtros,
            'consumidores' => $consumidores,
            'veiculos' => $veiculos,
            'motoristas' => $motoristas,
            'vendedores' => $vendedores,
            'pagamentos' => $pagamentos,
            'documentos' => $documentos,
            'consumidorFiltro' => $consumidorFiltro,
            'usuario_cliente' => $request->session()->get('logado_usuario')->clientes_id
        );

        $request->session()->flash('cardpagamentos', true);
        $request->session()->flash('carddocumentos', true);
        return view('admin.creditos')->with($params + ['titulopagina'=>'Abastecimentos']);
    }



    /**
     * Inserir novo registro ou alterar um existente
     *
     * @return Response
     */
    public function salvar(Request $request)
    {

        $dados = $request->all();
        if($request->session()->get('logado_usuario')->clientes_id) {
            $dados['clientes_id'] = $request->session()->get('logado_usuario')->clientes_id;
        }

        $dados['valor'] = Filter::amount($dados['valor']);
        if($request->has('valornegativo') and $dados['valor'] > 0) {
            $dados['valor'] = $dados['valor'] * -1;
        }

        if(!isset($dados['id']) and $dados['valor'] > 0) {
            $dados['status'] = 1;
        }

        $salvo = Creditos::salvar($dados);


		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        $registro = Creditos::find($salvo->id);
        if(!$registro->token) {
            $registro->token = Hash::make($salvo->id.date('s'));
            $registro->save();
        }

        Consumidores::find($salvo->consumidores_id)->calculaSaldo();

        //return back()->withErrors(array('success' => __('messages.save.success')));
        return redirect()->route('creditos',['id'=>$salvo->id])->withErrors(array('success' => __('messages.save.success')));

    }



	/**
	 * Deletar registro
	 *
	 * @param  int  $id
	 */
	 public function apagar($id)
	 {

        try {
            $registro = Creditos::find($id);
            $retorno = $registro->delete();
            Consumidores::find($registro->consumidores_id)->calculaSaldo();
        }
        catch(\Exception $e) {
			return back()->withErrors(array('error'=>__('messages.delete.error')));
        }

		// verifica se deu erro
		if ($retorno instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$retorno->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.delete.success')));

	 }


	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function atualizar(Request $request, $id, $campo)
	 {

	 	$registro = Creditos::findOrFail($id);
		$registro->$campo = $request->get('valor');

        $retorno = $registro->save();

        Consumidores::find($registro->consumidores_id)->calculaSaldo();

		// Retorna o status da operacao
		if($retorno == 0) {
			$response = array(
				'status' => 'error',
				'message' => __('messages.update.error'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		} else {
			$response = array(
				'status' => 'success',
				'message' => __('messages.update.success'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		}
	 }




	/**
	 * Ver registro
	 *
	 * @param  hash  $token
	 */
    public function ver(Request $request)
    {

        $registro = Creditos::where('token','=',$request->get('token'))->firstOrFail();

        $params = array(
            'registro' => $registro,
        );

        return view('creditos-ver')->with($params + ['titulopagina'=>'Fatura #'.$registro->dtcompra->format('ym').$registro->id]);

    }


}
