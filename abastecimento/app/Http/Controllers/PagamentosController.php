<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Pagamentos;


class PagamentosController extends Controller
{


    /**
     * Inserir novo registro ou alterar um existente
     *
     * @return Response
     */
    public function salvar(Request $request)
    {

        $dados = $request->all();
        $salvo = Pagamentos::salvar($dados);

		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        return back()->with(array('cardpagamentos'=>true))->withErrors(array('success' => __('messages.save.success')));
    }



	/**
	 * Deletar registro
	 *
	 * @param  int  $id
	 */
	 public function apagar($id)
	 {

        try {
            $registro = Pagamentos::find($id);
            $retorno = $registro->delete();
        }
        catch(\Exception $e) {
			return back()->withErrors(array('error'=>__('messages.delete.error')));
        }

		// verifica se deu erro
		if ($retorno instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('cardpagamentos'=>true))->withErrors(array('error'=>$retorno->getMessageBag()->first()));
		}

        return back()->with(array('cardpagamentos'=>true))->withErrors(array('success' => __('messages.delete.success')));

	 }


	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function atualizar(Request $request, $id, $campo)
	 {

	 	$registro = Pagamentos::findOrFail($id);
		$registro->$campo = $request->get('valor');

        $retorno = $registro->save();


		// Retorna o status da operacao
		if($retorno == 0) {
			$response = array(
				'status' => 'error',
				'message' => __('messages.update.error'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		} else {
			$response = array(
				'status' => 'success',
				'message' => __('messages.update.success'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		}
	 }


}
