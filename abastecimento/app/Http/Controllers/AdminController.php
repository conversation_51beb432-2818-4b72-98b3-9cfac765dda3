<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Carbon\Carbon;

use App\Consumidores;
use App\Creditos;

class AdminController extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        //Quantidade abastecimento
        $abastecimentogeral =  Creditos::queryGeral()->where('valor','>',0)->limit(10);
        $abastecimentos =  Creditos::queryGeral()->where('creditos.status','=',1)->where('valor','>',0);
        //Quantidade faturas pagas
        $faturageral = Creditos::queryGeral()->where('valor','<',0)->limit(10);
        $faturas = Creditos::queryGeral()->where('creditos.status','=',1)->where('valor','<',0);
        //Quantidade faturas pendentes
        $faturaspendentes = Creditos::queryGeral()->where('creditos.status','=',0)->where('valor','<',0);
        //Quantidade por veiculo
        $abastecimentosveiculo = Creditos::queryGeral()->selectRaw('SUM(valor) AS soma, veiculos_id')->where('creditos.status','=',1)->where('valor','>',0)->groupBy('veiculos_id');
        //Quantidade por tipo
        $abastecimentostipo = Creditos::queryGeral()->selectRaw('SUM(valor) AS soma, creditos.tipo')->where('creditos.status','=',1)->where('valor','>',0)->groupBy('creditos.tipo');

        $consumidores = Consumidores::select('consumidores.*', 'clientes.nome AS cliente')->join('clientes','clientes.id','=','consumidores.clientes_id')->where('consumidores.status','=',1)->orderBy('consumidores.nome','ASC');


        $clientes_id = $request->session()->get('logado_usuario')->clientes_id;
        if($clientes_id) {
            $abastecimentogeral->where('consumidores.clientes_id','=',$clientes_id);
            $abastecimentos->where('consumidores.clientes_id','=',$clientes_id);
            $faturageral->where('consumidores.clientes_id','=',$clientes_id);
            $faturas->where('consumidores.clientes_id','=',$clientes_id);
            $faturaspendentes->where('consumidores.clientes_id','=',$clientes_id);
            $abastecimentosveiculo->where('consumidores.clientes_id','=',$clientes_id);
            $abastecimentostipo->where('consumidores.clientes_id','=',$clientes_id);

            $consumidores = $consumidores->where('clientes_id','=',$clientes_id);
        }

        $consumidores_id = $request->get('consumidores_id',null);
        if($request->session()->get('logado') == 'consumidor') {
            $consumidores_id = $request->session()->get('logado_usuario')->id;
        }
        if($consumidores_id) {
            $abastecimentogeral->where('creditos.consumidores_id','=',$consumidores_id);
            $abastecimentos->where('creditos.consumidores_id','=',$consumidores_id);
            $faturageral->where('creditos.consumidores_id','=',$consumidores_id);
            $faturas->where('creditos.consumidores_id','=',$consumidores_id);
            $faturaspendentes->where('creditos.consumidores_id','=',$consumidores_id);
            $abastecimentosveiculo->where('creditos.consumidores_id','=',$consumidores_id);
            $abastecimentostipo->where('creditos.consumidores_id','=',$consumidores_id);
        }

        $dtde = $request->get('dtde',Carbon::now()->subMonths(6)->format('Y-m-d'));
        if($dtde) {
            $abastecimentogeral->where('creditos.dtcompra','>=',$dtde);
            $abastecimentos->where('creditos.dtcompra','>=',$dtde);
            $faturageral->where('creditos.dtcompra','>=',$dtde);
            $faturas->where('creditos.dtcompra','>=',$dtde);
            $faturaspendentes->where('creditos.dtcompra','>=',$dtde);
            $abastecimentosveiculo->where('creditos.dtcompra','>=',$dtde);
            $abastecimentostipo->where('creditos.dtcompra','>=',$dtde);
        }

        $dtate = $request->get('dtate',Carbon::now()->format('Y-m-d'));
        if($dtate) {
            $abastecimentogeral->where('creditos.dtcompra','<=',$dtate);
            $abastecimentos->where('creditos.dtcompra','<=',$dtate);
            $faturageral->where('creditos.dtcompra','<=',$dtate);
            $faturas->where('creditos.dtcompra','<=',$dtate);
            $faturaspendentes->where('creditos.dtcompra','<=',$dtate);
            $abastecimentosveiculo->where('creditos.dtcompra','<=',$dtate);
            $abastecimentostipo->where('creditos.dtcompra','<=',$dtate);
        }


        $abastecimentos_lista = $abastecimentogeral->orderBy('dtcompra','DESC')->get();
        $abastecimentos_qtd = $abastecimentos->count();
        $abastecimentos_valor = $abastecimentos->sum('valor');
        $abastecimentos_meses = $abastecimentos->selectRaw('YEAR(dtcompra) AS ano, MONTH(dtcompra) AS mes, SUM(valor) AS soma')->groupBy('ano')->groupBy('mes')->orderBy('dtcompra','ASC')->get();

        $faturas_lista = $faturageral->orderBy('dtcompra','DESC')->get();
        $faturas_qtd = $faturas->count();
        $faturas_valor = $faturas->sum('valor');
        $faturas_meses = $faturas->selectRaw('YEAR(dtcompra) AS ano, MONTH(dtcompra) AS mes, SUM(valor) AS soma')->groupBy('ano')->groupBy('mes')->orderBy('dtcompra','ASC')->get();
        $faturaspendentes_qtd = $faturaspendentes->count();
        $faturaspendentes_valor = $faturaspendentes->sum('valor');

        $abastecimentosveiculo_qtd = $abastecimentosveiculo->get()->count();
        $abastecimentosveiculo_valor = $abastecimentosveiculo->get();
        $abastecimentostipo_valor = $abastecimentostipo->get();
        $combustiveis = array(
            'gasolina' => 0,
            'alcool' => 0,
            'diesel' => 0
        );

        foreach($abastecimentostipo_valor as $row) {
            $combustiveis[$row->tipo] = number_format($row->soma/$abastecimentos_valor*100, 0);
        }


        $consumidores = $consumidores->get();

        $params = array(
            'abastecimentos_lista' => $abastecimentos_lista,
            'abastecimentos_qtd' => $abastecimentos_qtd,
            'abastecimentos_valor' => $abastecimentos_valor,
            'abastecimentos_meses' => $abastecimentos_meses,
            'faturas_lista' => $faturas_lista,
            'faturas_qtd' => $faturas_qtd,
            'faturas_valor' => $faturas_valor,
            'faturas_meses' => $faturas_meses,
            'faturaspendentes_qtd' => $faturaspendentes_qtd,
            'faturaspendentes_valor' => $faturaspendentes_valor,
            'abastecimentosveiculo_qtd' => $abastecimentosveiculo_qtd,
            'abastecimentosveiculo_valor' => $abastecimentosveiculo_valor,
            'combustiveis' => $combustiveis,
            'consumidores' => $consumidores,
            'consumidores_id' => $consumidores_id,
            'dtde' => $dtde,
            'dtate' => $dtate,
            'usuario_cliente' => $request->session()->get('logado_usuario')->clientes_id
        );

        return view('admin.home')->with($params);
    }




    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function login(Request $request)
    {

        $params = array(
        );

        return view('admin.login')->with($params);

    }






}
