<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Http\Controllers\Notificacao;
use Illuminate\Support\Facades\Hash;


use App\Gestores;
use App\Consumidores;

class LoginController extends Controller
{

    /**
     * Logar
     *
     * @return Response
     */
    public function verificaLogin(Request $request)
    {

        $email = $request->get('email');
        $senha = $request->get('senha');

        $tipousuario = 'gestor';
        $usuario = Gestores::select('gestores.*','clientes.nome AS cliente','clientes.url','clientes.imagem', 'clientes.cor')
                    ->leftJoin('clientes','clientes.id','=','gestores.clientes_id')
                    ->where('gestores.email','=',$email)
                    ->where('gestores.ativo','=',1)
                    ->first();

        $consumidor = Consumidores::select('consumidores.*','clientes.nome AS cliente','clientes.url','clientes.imagem', 'clientes.cor')
                    ->join('clientes','clientes.id','=','consumidores.clientes_id')
                    ->where('consumidores.email','=',$email)
                    ->where('consumidores.status','=',1)
                    ->first();


        if(empty($usuario) and empty($consumidor)) {
            return redirect()->route('login')->withErrors(array('error' => __('auth.failed')));
        }

        if (!empty($usuario) and !Hash::check($senha, $usuario->senha)) {
            return redirect()->route('login')->withErrors(array('error' => __('auth.failedpassword')));
        }

        if (!empty($consumidor) and !Hash::check($senha, $consumidor->senha)) {
            return redirect()->route('login')->withErrors(array('error' => __('auth.failedpassword')));
        }

        $request->session()->forget('logado');
        $request->session()->forget('logado_usuario');

        if(!empty($consumidor)) {
            $tipousuario = 'consumidor';
            $usuario = $consumidor;
        }

        if($tipousuario == 'gestor' and $usuario->clientes_id != null) {
            $tipousuario = 'cliente';
        }

        $request->session()->put('logado', $tipousuario);
        $request->session()->put('logado_usuario', $usuario);


        return redirect()->route('admin');
    }


    /**
     * Sair sessao
     *
     * @return Response
     */
    public function logout(Request $request)
    {
        $request->session()->forget('logado');
        $request->session()->forget('logado_usuario');

        return redirect()->route('home');

    }


}
