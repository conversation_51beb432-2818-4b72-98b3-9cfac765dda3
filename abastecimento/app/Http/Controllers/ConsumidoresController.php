<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

use App\Consumidores;
use App\Clientes;
use App\Creditos;


class ConsumidoresController extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id = null)
    {


        $registro = new Consumidores;
        if($id) {
            $registro = Consumidores::findOrFail($id);
        }
        if($request->session()->has('registro')) {
            $registro = new Consumidores($request->session()->get('registro'));
        }

        $dados = Consumidores::select('consumidores.*', 'clientes.nome AS cliente')
                    ->join('clientes','clientes.id','=','consumidores.clientes_id')
                    ->orderBy('nome', 'ASC');


        $clientes_id = $request->session()->get('logado_usuario')->clientes_id;
        if($clientes_id) {
            $dados->where('consumidores.clientes_id','=',$clientes_id);
        }

        $termo = $request->get('termo',null);
        if($termo) {
            $dados->whereRaw('(consumidores.nome LIKE "%'.$termo.'%" OR clientes.nome LIKE "%'.$termo.'%")');
        }

        $total = $dados->count();
		$dados = $dados->paginate(30);

        $filtros = array(
            'page' => $request->get('page',0),
            'termo' => $termo
		);

		$paginacao = $dados->appends($filtros + ['total'=>$total])->links();

        $clientes = Clientes::where('ativo','=',1)->orderBy('nome','ASC')->get();
        $params = $filtros + array(
            'dados' => $dados,
            'registro' => $registro,
            'filtros' => $filtros,
            'clientes' => $clientes,
            'usuario_cliente' => $request->session()->get('logado_usuario')->clientes_id
        );

        return view('admin.consumidores')->with($params + ['titulopagina'=>'Consumidores']);
    }



    /**
     * Inserir novo registro ou alterar um existente
     *
     * @return Response
     */
    public function salvar(Request $request)
    {

        $dados = $request->all();
        if($request->session()->get('logado_usuario')->clientes_id) {
            $dados['clientes_id'] = $request->session()->get('logado_usuario')->clientes_id;
        }

        if($request->get('senha') != '') {
            $dados['senha'] = Hash::make($request->get('senha'));
        }
        else {
            unset($dados['senha']);
        }

        $salvo = Consumidores::salvar($dados);


		// verifica se deu erro
		if ($salvo instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$salvo->getMessageBag()->first()));
		}

        $registro = Consumidores::find($salvo->id);
        if(!$registro->token) {
            $registro->token = Hash::make($salvo->id);
            $registro->save();
        }

        return back()->withErrors(array('success' => __('messages.save.success')));
    }



	/**
	 * Deletar registro
	 *
	 * @param  int  $id
	 */
	 public function apagar($id)
	 {

        try {
            $retorno = Consumidores::find($id)->delete();
        }
        catch(\Exception $e) {
			return back()->withErrors(array('error'=>__('messages.delete.error')));
        }

		// verifica se deu erro
		if ($retorno instanceof \Illuminate\Support\MessageBag) {
			return back()->with(array('registro'=>$dados))->withErrors(array('error'=>$retorno->getMessageBag()->first()));
		}

        return back()->withErrors(array('success' => __('messages.delete.success')));

	 }


	/**
	 * Alterar dado registro
	 *
	 * @param  int  $id
	 */
	 public function atualizar(Request $request, $id, $campo)
	 {

	 	$registro = Consumidores::findOrFail($id);
		$registro->$campo = $request->get('valor');

        $retorno = $registro->save();


		// Retorna o status da operacao
		if($retorno == 0) {
			$response = array(
				'status' => 'error',
				'message' => __('messages.update.error'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		} else {
			$response = array(
				'status' => 'success',
				'message' => __('messages.update.success'),
			);
			// retorna json
			return response()->json(array('data' => $response));
		}
	 }


	/**
	 * Gerar automaticamente na rotina
	 *
	 */
    public function gerarfatura(Request $request)
    {

        //passou do limite OU chegou no dia
        $consumidores = Consumidores::where('saldo','>',0)->where('status','=',1)
                            ->whereRaw('(saldo > valorlimitepagamento AND valorlimitepagamento > 0) OR diapagamento = '.date('d'))
                            ->get();

        //dd($consumidores);
        foreach($consumidores as $consumidor) {
            // verifica se tem fatura pendente
            $temfaturapendente = Creditos::where('consumidores_id','=',$consumidor->id)->where('valor','<',0)->where('status','=',0)->first();
            if(!empty($temfaturapendente)) {
                echo $consumidor->nome.' - FATURA PENDENTE '.$temfaturapendente->valor.'<br />';
                break;
            }

            $dados = array(
                'consumidores_id' => $consumidor->id,
                'valor' => (-1) * ($consumidor->saldo > $consumidor->valorlimitepagamento ? $consumidor->valorlimitepagamento : $consumidor->saldo),
                'dtcompra'=> date('Y-m-d'),
                'token' => Hash::make($consumidor->id.date('s')),
                'status' => 0 //pendente
            );

            $fatura = Creditos::salvar($dados);
            $consumidor->calculaSaldo();

            echo $consumidor->nome.' - '.$fatura->valor.'<br />';
        }

        echo '------ Verificados '.count($consumidores).' consumidores';
        return true;
    }


}
