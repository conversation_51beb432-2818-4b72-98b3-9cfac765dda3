<?php

namespace App\Http\Controllers;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class Notificacao extends Mailable
{
    use Queueable, SerializesModels;

    public $variaveis;
    public $template;
    public $assunto;

    /**
     * Create a new message instance.
     *
     * @param  Array  $variaveis
     * @return void
     */
    public function __construct(array $variaveis, $template, $assunto)
    {
        $this->variaveis = $variaveis;
        $this->template = $template;
        $this->assunto = $assunto;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject($this->assunto)->view($this->template);
    }
}