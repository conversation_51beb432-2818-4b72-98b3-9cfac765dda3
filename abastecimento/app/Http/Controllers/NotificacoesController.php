<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Http\Controllers\Notificacao;

use App\Notificacoes;
use App\Pontos;
use App\Colaboradores;
use App\Participantes;
use App\Premios;

class NotificacoesController extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id = null)
    {


        if($request->session()->get('logado') == 'cliente') {
            return back()->withErrors(array('error' => 'Sem permissão'));
        }


        $dados = Notificacoes::select('notificacoes.*')
                    ->orderBy('created_at', 'DESC');

        $termo = $request->get('termo',null);
        if($termo) {
            $dados->whereRaw('(email LIKE "%'.$termo.'%" OR nome LIKE "%'.$termo.'%" OR assunto LIKE "%'.$termo.'%" OR tipo LIKE "%'.$termo.'%")');
        }


        $total = $dados->count();
		$dados = $dados->paginate(30);


        $filtros = array(
            'page' => $request->get('page',0),
            'termo' => $termo
		);

		$paginacao = $dados->appends($filtros + ['total'=>$total])->links();


        $params = $filtros + array(
            'dados' => $dados,
            'filtros' => $filtros
        );

        return view('admin.notificacoes')->with($params);
    }




	/**
	 * Deletar registro
	 *
	 * @param  int  $id
	 */
    public function apagar($id, Request $request)
    {
       try {
           $retorno = Notificacoes::find($id)->delete();
       }
       catch(\Exception $e) {
           return back()->withErrors(array('error'=>__('messages.delete.error')));
       }


       return back()->with($request->all())->withErrors(array('success' => __('messages.delete.success')));

    }



	/**
	 * Visualizar registro
	 *
	 * @param  int  $id
	 */
    public function visualizar($id, Request $request)
    {

        $notificacao = Notificacoes::find($id);
        $dados = unserialize($notificacao->variaveis);

        try {
        switch($notificacao->tipo) {
            case 'pontonovo':
                $dados['participante'] = Participantes::find($dados['participantes_id']);
                $dados['colaborador'] = $dados['participante']->colaborador;
                $dados['cliente'] = $dados['colaborador']->cliente;
                $dados['saldo'] = $dados['participante']->pontuacao();
            break;
            case 'premioresgate':
                $dados['participante'] = Participantes::find($dados['participantes_id']);
                $dados['premio'] = Premios::find($dados['premios_id']);
                $dados['colaborador'] = $dados['participante']->colaborador;
                $dados['cliente'] = $dados['colaborador']->cliente;
            break;
            case 'colaboradorativo':
                $dados['colaborador'] = Colaboradores::find($dados['id']);
                $dados['cliente'] = $dados['colaborador']->cliente;
            break;
            case 'novoresgate':
                $dados['participante'] = Participantes::find($dados['participantes_id']);
                $dados['premio'] = Premios::find($dados['premios_id']);
                $dados['colaborador'] = $dados['participante']->colaborador;
                $dados['cliente'] = $dados['colaborador']->cliente;
            case 'resgatestatus':
                $dados['ponto'] = Pontos::find($dados['pontos_id']);
                $dados['participante'] = Participantes::find($dados['ponto']->participantes_id);
                $dados['premio'] = Premios::find($dados['ponto']->premios_id);
                $dados['colaborador'] = $dados['participante']->colaborador;
                $dados['cliente'] = $dados['colaborador']->cliente;
            break;
        }
        }
        catch(\Exception $e) {
            echo ($e->getMessage());
            return false;
        }


        $params = array(
            'variaveis' => $dados
        );

        $mailable = new Notificacao($dados, 'email.'.$notificacao->template, $notificacao->assunto);
        return ($mailable);

    }


	/**
	 * Enviar registro
	 *
	 * @param  int  $id
	 */
    public function enviar($id, Request $request)
    {

        $notificacao = Notificacoes::find($id);
        $mailable = $this->visualizar($id, $request);

        if($mailable == false) {
            $notificacao->status = 2; // Mudar status para falha
            $notificacao->save();
            return false;
        }

        if($notificacao->email != '') {
            try {
                    $envio = Mail::to($notificacao->email)->send($mailable);
            }
            catch(\Exception $e) {
                $notificacao->status = 2; // Mudar status para falha
                $notificacao->save();
                return false;
            }
        }


        // Mudar status para enviado
        $notificacao->status = 1;
        $notificacao->save();

        echo 'Enviado! '.$notificacao->id.'<br>';
        return true;
    }




	/**
	 * Enviar notificacoes
	 *
	 */
    public function envianotificacoes(Request $request)
    {

        $notificacoes = Notificacoes::where('status','=',0)->orderBy('prioridade','ASC')->limit(2)->get();

        foreach($notificacoes as $notificacao) {
            $this->enviar($notificacao->id, $request);
        }

        echo 'Finalizado';
        return true;
    }



}
