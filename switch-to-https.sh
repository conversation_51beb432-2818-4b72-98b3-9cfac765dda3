#!/bin/bash

echo "Alterando configuração para HTTPS..."

# Restaurar configuração HTTPS se existir backup
if [ -f "docker/nginx/app-https-backup.conf" ]; then
    cp docker/nginx/app-https-backup.conf docker/nginx/app.conf
    echo "Configuração HTTPS restaurada do backup."
else
    echo "Backup não encontrado. Reconfigurando HTTPS..."
    # Reconfigurar HTTPS manualmente se necessário
fi

# Alterar porta no docker-compose.yml
sed -i 's/"8000:80"/"8443:443"/g' docker-compose.yml

echo "Reiniciando containers..."
sudo docker compose down
sudo docker compose up -d

echo "✅ Alterado para HTTPS!"
echo "🌐 Acesse: https://localhost:8443"
